/**
 * Test script to show what the Attack List API returns
 * This will help visualize the data structure and content
 */

const API_KEY = 'api_277249e31e1bcfd500ee3011fcd7a7b8'; // Your API key
const BASE_URL = 'http://localhost:3000'; // Adjust if different

async function testAttackListAPI() {
  console.log('🎯 Testing Attack List API...\n');
  
  try {
    // Test the messages-to-send endpoint
    console.log('📡 Calling /api/chrome-extension/messages-to-send...');
    const response = await fetch(`${BASE_URL}/api/chrome-extension/messages-to-send`, {
      method: 'GET',
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    console.log('✅ API Response received!\n');
    console.log('📊 ATTACK LIST DATA:');
    console.log('='.repeat(50));
    console.log(JSON.stringify(data, null, 2));
    console.log('='.repeat(50));
    
    // Parse and display in a more readable format
    if (data.success) {
      console.log('\n📋 SUMMARY:');
      console.log(`• Total messages ready: ${data.count}`);
      console.log(`• Total contacts ready: ${data.metadata.totalReadyContacts}`);
      console.log(`• Smart Focus enabled: ${data.metadata.smartFocusEnabled}`);
      console.log(`• Timestamp: ${data.metadata.timestamp}`);
      
      if (data.data && data.data.length > 0) {
        console.log('\n🎯 MESSAGES TO SEND:');
        data.data.forEach((message, index) => {
          console.log(`\n${index + 1}. Contact: @${message.username}`);
          console.log(`   ID: ${message.id}`);
          console.log(`   Type: ${message.type}`);
          console.log(`   Message: "${message.message}"`);
          
          if (message.followUpId) {
            console.log(`   Follow-up ID: ${message.followUpId}`);
          }
          
          if (message.batchMessages) {
            console.log(`   Batch Messages (${message.batchMessages.length}):`);
            message.batchMessages.forEach((batch, bIndex) => {
              console.log(`     ${bIndex + 1}. "${batch.message}"`);
            });
          }
        });
      } else {
        console.log('\n📭 No messages ready to send at this time.');
      }
    } else {
      console.log(`❌ API Error: ${data.error}`);
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error.message);
    console.log('\n💡 Make sure:');
    console.log('• Dashboard is running on localhost:3000');
    console.log('• API key is correct');
    console.log('• Database has some test data');
  }
}

// Run the test
testAttackListAPI();