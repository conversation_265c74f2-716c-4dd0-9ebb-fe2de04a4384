// Simple test script to trigger queue processing
const fetch = require('node-fetch');

async function testQueue() {
  try {
    console.log('🚀 Testing queue processing...');
    
    // Test queue stats
    console.log('\n1. Getting queue stats...');
    const statsResponse = await fetch('http://localhost:3000/api/test/follower-queue', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'get_stats' })
    });
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ Queue stats:', statsData.data);
    } else {
      console.log('❌ Failed to get stats:', statsResponse.status);
    }
    
    // Start queue processor
    console.log('\n2. Starting queue processor...');
    const startResponse = await fetch('http://localhost:3000/api/test/follower-queue', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'start_queue' })
    });
    
    if (startResponse.ok) {
      const startData = await startResponse.json();
      console.log('✅ Queue processor started:', startData.message);
    } else {
      console.log('❌ Failed to start queue:', startResponse.status);
    }
    
    // Process queue manually
    console.log('\n3. Processing queue manually...');
    const processResponse = await fetch('http://localhost:3000/api/test/follower-queue', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'process_queue' })
    });
    
    if (processResponse.ok) {
      const processData = await processResponse.json();
      console.log('✅ Queue processing triggered:', processData.message);
    } else {
      console.log('❌ Failed to process queue:', processResponse.status);
    }
    
    // Get final stats
    console.log('\n4. Getting final stats...');
    const finalStatsResponse = await fetch('http://localhost:3000/api/test/follower-queue', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'get_stats' })
    });
    
    if (finalStatsResponse.ok) {
      const finalStatsData = await finalStatsResponse.json();
      console.log('✅ Final queue stats:', finalStatsData.data);
    } else {
      console.log('❌ Failed to get final stats:', finalStatsResponse.status);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testQueue();
