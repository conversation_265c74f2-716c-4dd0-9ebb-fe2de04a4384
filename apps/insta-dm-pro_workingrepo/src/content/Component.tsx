import { useEffect, useRef } from 'react'
import Browser from 'webextension-polyfill'
import { GeneralConfig } from '@shared/config'
import { MessageTypes } from '@shared/messaging'
import delay from '@background/Utils/delay'

const logNow = (...all: any) => {
  try {
    const timestamp = new Date().toISOString()
    console.log(`${timestamp}: `, ...all)
  } catch (error) {
    console.warn('error :: ', error)
  }
}

let isLoopOn = false

const Component = () => {
  const botRunning = useRef(false)

  // Initialize localStorage-based Instagram DM handler
  useEffect(() => {
    logNow('Initializing content script with localStorage approach')

    const script = document.createElement('script')
    script.src = Browser.runtime.getURL('inject/ijsource-localstorage.js')
    script.onload = () => {
      logNow('ijsource-localstorage.js loaded successfully')
      script.remove()
    }
    script.onerror = () => {
      logNow('Failed to load ijsource-localstorage.js')
      script.remove()
    }
    document.head.appendChild(script)
  }, [])

  const readInstagramInbox = async () => {
    let csrf_token = await Browser.runtime.sendMessage({
      type: MessageTypes.GET_COOKIE,
      data: { url: 'https://www.instagram.com', name: 'csrftoken' },
    })

    const inboxResponse = await fetch('https://i.instagram.com/api/v1/news/inbox/', {
      credentials: 'include',
      headers: {
        accept: 'application/json, text/plain, */*',
        Referer: 'https://www.instagram.com/',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'x-asbd-id': '129477',
        'X-IG-App-ID': '936619743392459',
        'x-instagram-ajax': '1',
        'X-CSRFToken': csrf_token,
        'x-requested-with': 'XMLHttpRequest',
      },
      body: null,
      method: 'POST',
    })
    const inboxData = await inboxResponse.json()
    return inboxData
  }

  const getInstagramStories = async (conf: any) => {
    const inbox = await readInstagramInbox()
    let stories = []
    if (inbox.status && inbox.status === 'ok') {
      let total_stories = [...inbox.new_stories, ...inbox.old_stories]
      for (let i = 0; i < total_stories.length; i++) {
        const story = total_stories[i]
        if (story.story_type === 101 && story.type === 3) {
          stories.push({
            id: story.args.profile_id,
            username: story.args.profile_name,
            profile_image: story.args.profile_image,
            type: 0,
            timestamp: story.args.timestamp,
          })
        }
      }
      return {
        status: 'success',
        data: stories,
      }
    } else {
      return {
        status: 'failed',
        message: 'Failed to fetch inbox',
      }
    }
  }

  const getProfileUrl = async () => {
    // Extract username from current Instagram URL
    const currentUrl = window.location.href
    const pathname = window.location.pathname
    const pathParts = pathname.split('/').filter(Boolean)
    
    // Add diagnostic logging
    logNow('🔍 PROFILE_URL_DEBUG: Getting profile URL', {
      currentUrl,
      pathname,
      pathParts,
      pathPartsLength: pathParts.length
    })
    
    // Handle different Instagram URL patterns
    if (pathParts.length > 0 && pathParts[0] !== 'direct' && pathParts[0] !== 'accounts') {
      const username = pathParts[0]
      logNow('🔍 PROFILE_URL_DEBUG: Found username in URL:', username)
      // Return just the username (first path segment after domain)
      return username
    }
    
    // If no username in URL, try to find the user's profile link in navigation
    logNow('🔍 PROFILE_URL_DEBUG: No username in URL, searching for profile link in navigation')
    
    try {
      // Look for profile link in the main navigation
      const profileLinks = document.querySelectorAll('a[href^="/"][href*="/"]')
      
      for (const link of profileLinks) {
        const href = (link as HTMLAnchorElement).href
        const linkPath = new URL(href).pathname
        const linkParts = linkPath.split('/').filter(Boolean)
        
        // Check if this looks like a profile link (single username, not special pages)
        if (linkParts.length === 1 &&
            !linkParts[0].includes('direct') &&
            !linkParts[0].includes('explore') &&
            !linkParts[0].includes('accounts') &&
            !linkParts[0].includes('p/') &&
            !linkParts[0].includes('reel/') &&
            linkParts[0].length > 0) {
          
          // Additional check: see if this link has profile-like attributes
          const linkElement = link as HTMLElement
          const hasProfileImage = linkElement.querySelector('img') !== null
          const isInNavigation = linkElement.closest('nav') !== null ||
                                linkElement.closest('[role="navigation"]') !== null ||
                                linkElement.closest('header') !== null
          
          if (hasProfileImage || isInNavigation) {
            logNow('🔍 PROFILE_URL_DEBUG: Found potential profile link:', linkParts[0])
            return linkParts[0]
          }
        }
      }
      
      // Fallback: look for any link that might be the user's profile
      const userProfileLink = document.querySelector('a[href^="/"][aria-label*="profile" i], a[href^="/"] img[alt*="profile" i]')?.closest('a') as HTMLAnchorElement
      if (userProfileLink) {
        const href = userProfileLink.href
        const linkPath = new URL(href).pathname
        const linkParts = linkPath.split('/').filter(Boolean)
        if (linkParts.length === 1) {
          logNow('🔍 PROFILE_URL_DEBUG: Found profile link via aria-label/alt:', linkParts[0])
          return linkParts[0]
        }
      }
      
    } catch (error) {
      logNow('🔍 PROFILE_URL_DEBUG: Error searching for profile link:', error)
    }
    
    // Return empty string if no username found
    logNow('🔍 PROFILE_URL_DEBUG: No username found anywhere, returning empty string')
    return ''
  }

  const openFollowersPanel = async (username: string) => {
    let selector = `a[href^="/${username}/followers"][role="link"]`
    let result = {
      status: 'failed',
      message: 'Not started yet',
    }
    let followers_list_btn = document.querySelector(selector)
    if (!followers_list_btn) {
      await delay(2000)
      followers_list_btn = document.querySelector(selector)
    }
    if (!followers_list_btn) {
      console.log('ERROR couldnt find follower button')
      result.status = 'error'
      result.message = 'Failed to find followers button.'
      return result
    }
    ; (followers_list_btn as HTMLElement).click()
    await delay(4000)
    let dialog = document.querySelector('[role="dialog"]')
    if (!dialog) {
      ; (followers_list_btn as HTMLElement).click()
      await delay(4000)
      dialog = document.querySelector('[role="dialog"]')
    }
    if (!dialog) {
      result.status = 'error'
      result.message = 'Failed to find dialog.'
      return result
    }
    result.status = 'success'
    result.message = 'Followers dialog opened.'
    return result
  }

  const getFollowersList = async (count: number = 100, skipCount: number = 0) => {
    // 🔥 VISIBILITY CHECK: Warn if tab is not visible (common issue)
    if (document.hidden || document.visibilityState === 'hidden') {
      logNow('⚠️ WARNING: Tab is not visible! Scrolling may not work properly in background tabs.')
      logNow('💡 SOLUTION: Keep the Instagram tab active/visible during scraping for best results.')
    } else {
      logNow('✅ Tab is visible, scrolling should work properly.')
    }
    
    const dialog = document.querySelector('[role="dialog"]')
    if (!dialog) {
      logNow('❌ No followers dialog found')
      return []
    }

    logNow(`🔄 Starting progressive follower scraping - Target: ${count} followers, Skip: ${skipCount} already scraped`)
    
    // 🔥 ADD VISIBILITY CHANGE LISTENER: Monitor if tab becomes hidden during scraping
    let visibilityWarningShown = false
    const handleVisibilityChange = () => {
      if (document.hidden && !visibilityWarningShown) {
        logNow('⚠️ TAB BECAME HIDDEN DURING SCRAPING! This may slow down or break the scraping process.')
        logNow('💡 Please keep the Instagram tab active for reliable scraping.')
        visibilityWarningShown = true
      }
    }
    document.addEventListener('visibilitychange', handleVisibilityChange)
    const followers = new Set<string>() // Use Set to avoid duplicates
    const allScrapedFollowers = new Set<string>() // Track all followers we've seen (including skipped ones)
    let scrollAttempts = 0
    const maxScrollAttempts = 100 // Increased for progressive scraping
    let noNewFollowersCount = 0
    const maxNoNewFollowersAttempts = 8 // Increased tolerance for progressive scraping

    // Find the scrollable container within the dialog by looking for overflow-y: scroll
    let scrollableContainer: Element | null = null
    
    // Method 1: Look for elements with overflow-y: scroll in computed styles
    const allDivs = dialog.querySelectorAll('div')
    for (const div of allDivs) {
      const computedStyle = window.getComputedStyle(div)
      if (computedStyle.overflowY === 'scroll' || computedStyle.overflow === 'scroll') {
        scrollableContainer = div
        logNow('✅ Found scrollable container with overflow-y: scroll')
        break
      }
    }
    
    // Method 2: Fallback - look for inline style with overflow-y: scroll
    if (!scrollableContainer) {
      scrollableContainer = dialog.querySelector('[style*="overflow-y: scroll"]') ||
                           dialog.querySelector('[style*="overflow-y:scroll"]') ||
                           dialog.querySelector('[style*="overflow: scroll"]')
      if (scrollableContainer) {
        logNow('✅ Found scrollable container with inline overflow-y: scroll style')
      }
    }
    
    // Method 3: Final fallback to dialog itself
    if (!scrollableContainer) {
      scrollableContainer = dialog
      logNow('⚠️ Using dialog as fallback scrollable container')
    }

    logNow('✅ Found scrollable container, starting progressive scroll')

    // Phase 1: Scroll past already scraped followers if skipCount > 0
    if (skipCount > 0) {
      logNow(`🚀 Phase 1: Scrolling past first ${skipCount} already scraped followers...`)
      
      while (allScrapedFollowers.size < skipCount && scrollAttempts < maxScrollAttempts) {
        const currentFollowerCount = allScrapedFollowers.size
        
        // Collect currently visible followers
        const links = dialog.querySelectorAll('a[href^="/"]')
        
        for (const link of links) {
          const linkElement = link as HTMLAnchorElement
          const href = linkElement.href
          
          // Filter out non-profile links
          if (href.includes('/') && 
              !href.includes('/p/') && 
              !href.includes('/reel/') && 
              !href.includes('/tv/') &&
              !href.includes('/stories/') &&
              !href.includes('/direct/') &&
              !href.includes('/explore/')) {
            
            // Extract username from URL
            const urlParts = href.split('/').filter(Boolean)
            if (urlParts.length > 0) {
              const username = urlParts[urlParts.length - 1]
              // Basic validation for username format
              if (username && username.length > 0 && !username.includes('?') && !username.includes('#')) {
                allScrapedFollowers.add(href)
              }
            }
          }
        }

        logNow(`📊 Phase 1: Scrolled past ${allScrapedFollowers.size}/${skipCount} followers (attempt ${scrollAttempts + 1})`)

        // Break if we've scrolled past enough followers
        if (allScrapedFollowers.size >= skipCount) {
          logNow(`✅ Phase 1 complete: Scrolled past ${skipCount} already scraped followers`)
          break
        }

        // Check if we got new followers this round
        if (allScrapedFollowers.size === currentFollowerCount) {
          noNewFollowersCount++
          logNow(`⚠️ Phase 1: No new followers found this round (${noNewFollowersCount}/${maxNoNewFollowersAttempts})`)
          
          if (noNewFollowersCount >= maxNoNewFollowersAttempts) {
            logNow(`⚠️ Phase 1: Stopping due to no progress after ${maxNoNewFollowersAttempts} attempts`)
            break
          }
        } else {
          noNewFollowersCount = 0 // Reset counter when we find new followers
        }

        // Scroll down to load more followers
        scrollAttempts++
        logNow(`🔄 Phase 1: Scrolling to load more followers (attempt ${scrollAttempts})`)
        
        const scrollElement = scrollableContainer as HTMLElement
        
        // 🔥 AGGRESSIVE SCROLLING: Multiple methods to ensure scrolling works in background tabs
        
        // Method 1: Direct scrollTop manipulation
        scrollElement.scrollTop = scrollElement.scrollHeight
        
        // Method 2: Force focus and scroll (prevents throttling)
        scrollElement.focus()
        scrollElement.scrollIntoView({ behavior: 'smooth', block: 'end' })
        
        // Method 3: Use programmatic scrolling with smooth behavior override
        scrollElement.scrollTo({
          top: scrollElement.scrollHeight,
          behavior: 'auto' // Use 'auto' to bypass animation throttling
        })
        
        await delay(1000) // Shorter delay for skipping phase
        
        // Method 4: If no progress, try multiple event types
        if (allScrapedFollowers.size === currentFollowerCount) {
          // Wheel event
          const wheelEvent = new WheelEvent('wheel', {
            deltaY: 2000,
            bubbles: true,
            cancelable: true
          })
          scrollElement.dispatchEvent(wheelEvent)
          
          // Touch events for mobile-like scrolling
          const touchStart = new TouchEvent('touchstart', { bubbles: true })
          const touchMove = new TouchEvent('touchmove', { bubbles: true })
          const touchEnd = new TouchEvent('touchend', { bubbles: true })
          scrollElement.dispatchEvent(touchStart)
          scrollElement.dispatchEvent(touchMove)
          scrollElement.dispatchEvent(touchEnd)
          
          // Keyboard events as fallback
          const keyEvent = new KeyboardEvent('keydown', { 
            key: 'End', 
            bubbles: true 
          })
          scrollElement.dispatchEvent(keyEvent)
          await delay(1500)
        }
      }
      
      // Reset counters for phase 2
      noNewFollowersCount = 0
      logNow(`🎯 Phase 1 Results: Scrolled past ${allScrapedFollowers.size} followers using ${scrollAttempts} scroll attempts`)
    }

    // Phase 2: Collect the next batch of new followers
    logNow(`🚀 Phase 2: Collecting next ${count} new followers...`)
    
    while (followers.size < count && scrollAttempts < maxScrollAttempts && noNewFollowersCount < maxNoNewFollowersAttempts) {
      const currentFollowerCount = followers.size
      
      // Collect currently visible followers
      const links = dialog.querySelectorAll('a[href^="/"]')
      logNow(`📋 Found ${links.length} total links in dialog (attempt ${scrollAttempts + 1})`)
      
      let newFollowersThisRound = 0
      for (const link of links) {
        const linkElement = link as HTMLAnchorElement
        const href = linkElement.href
        
        // Filter out non-profile links
        if (href.includes('/') && 
            !href.includes('/p/') && 
            !href.includes('/reel/') && 
            !href.includes('/tv/') &&
            !href.includes('/stories/') &&
            !href.includes('/direct/') &&
            !href.includes('/explore/')) {
          
          // Extract username from URL
          const urlParts = href.split('/').filter(Boolean)
          if (urlParts.length > 0) {
            const username = urlParts[urlParts.length - 1]
            // Basic validation for username format
            if (username && username.length > 0 && !username.includes('?') && !username.includes('#')) {
              // Add to all scraped followers tracker
              allScrapedFollowers.add(href)
              
              // Only add to new followers if we've passed the skip threshold
              if (allScrapedFollowers.size > skipCount && !followers.has(href)) {
                followers.add(href)
                newFollowersThisRound++
              }
            }
          }
        }
      }

      logNow(`📊 Phase 2: Collected ${followers.size}/${count} new followers (+${newFollowersThisRound} this round), Total seen: ${allScrapedFollowers.size}`)

      // Check if we got new followers this round
      if (followers.size === currentFollowerCount) {
        noNewFollowersCount++
        logNow(`⚠️ Phase 2: No new followers found this round (${noNewFollowersCount}/${maxNoNewFollowersAttempts})`)
      } else {
        noNewFollowersCount = 0 // Reset counter when we find new followers
      }

      // Break if we have enough followers
      if (followers.size >= count) {
        logNow(`✅ Phase 2 complete: Reached target count: ${followers.size}/${count}`)
        break
      }

      // Scroll down to load more followers
      scrollAttempts++
      logNow(`🔄 Phase 2: Scrolling to load more followers (attempt ${scrollAttempts})`)
      
      // Try multiple scroll methods
      const scrollElement = scrollableContainer as HTMLElement
      
      // 🔥 PHASE 2 AGGRESSIVE SCROLLING: Multiple methods for reliable background tab scrolling
      
      // Method 1: Direct scrollTop manipulation
      scrollElement.scrollTop = scrollElement.scrollHeight
      
      // Method 2: Force focus and programmatic scroll
      scrollElement.focus()
      scrollElement.scrollTo({
        top: scrollElement.scrollHeight,
        behavior: 'auto' // Bypass animation throttling in background tabs
      })
      
      await delay(1500) // Wait for Instagram to load new content
      
      // Method 3: If first methods didn't work, try multiple event types
      if (followers.size === currentFollowerCount) {
        logNow('🔄 Phase 2: Trying aggressive scroll methods for background tab')
        
        // Wheel event
        const wheelEvent = new WheelEvent('wheel', {
          deltaY: 1000,
          bubbles: true,
          cancelable: true
        })
        scrollElement.dispatchEvent(wheelEvent)
        
        // Focus event to ensure element is active
        const focusEvent = new FocusEvent('focus', { bubbles: true })
        scrollElement.dispatchEvent(focusEvent)
        
        // Additional scroll attempt with different delta
        const wheelEvent2 = new WheelEvent('wheel', {
          deltaY: 1500,
          bubbles: true,
          cancelable: true
        })
        scrollElement.dispatchEvent(wheelEvent2)
        await delay(2000)
      }
      
      // Method 3: If still no new content, try focusing and using arrow keys
      if (followers.size === currentFollowerCount) {
        logNow('🔄 Phase 2: Trying keyboard scroll method')
        scrollElement.focus()
        for (let i = 0; i < 10; i++) {
          const arrowDownEvent = new KeyboardEvent('keydown', {
            key: 'ArrowDown',
            code: 'ArrowDown',
            keyCode: 40,
            bubbles: true
          })
          scrollElement.dispatchEvent(arrowDownEvent)
          await delay(100)
        }
        await delay(1500)
      }
    }

    const finalFollowers = Array.from(followers).slice(0, count)
    const totalPosition = skipCount + finalFollowers.length
    
    logNow(`🎯 Progressive scraping completed:`)
    logNow(`   📊 Skipped: ${skipCount} already scraped followers`)
    logNow(`   📊 Collected: ${finalFollowers.length}/${count} new followers`)
    logNow(`   📊 Total position: ${totalPosition} followers`)
    logNow(`   📊 Scroll attempts: ${scrollAttempts}`)
    
    if (finalFollowers.length < count) {
      if (scrollAttempts >= maxScrollAttempts) {
        logNow(`⚠️ Stopped due to max scroll attempts (${maxScrollAttempts})`)
      } else if (noNewFollowersCount >= maxNoNewFollowersAttempts) {
        logNow(`⚠️ Stopped due to no new followers found after ${maxNoNewFollowersAttempts} attempts`)
      }
    }
    
    // 🔥 CLEANUP: Remove visibility change listener to prevent memory leaks
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    logNow('✅ Visibility listener cleaned up')
    
    return {
      followers: finalFollowers,
      totalPosition: totalPosition,
      skippedCount: skipCount,
      collectedCount: finalFollowers.length
    }
  }

  const createGroupThread = async (recipent_id: string) => {
    let u = 'https://i.instagram.com/api/v1/direct_v2/create_group_thread/'
    let csrf_token = await Browser.runtime.sendMessage({
      type: MessageTypes.GET_COOKIE,
      data: { url: 'https://www.instagram.com', name: 'csrftoken' },
    })
    const s = { recipient_users: '["'.concat(recipent_id, '"]') }
    const l = new URLSearchParams([...Object.entries(s)]).toString()

    let response = await fetch(u, {
      credentials: 'include',
      headers: {
        accept: 'application/json, text/plain, */*',
        'content-type': 'application/x-www-form-urlencoded',
        Referer: 'https://www.instagram.com/',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'x-asbd-id': '129477',
        'X-IG-App-ID': '936619743392459',
        'x-instagram-ajax': '1',
        'X-CSRFToken': csrf_token,
        'x-requested-with': 'XMLHttpRequest',
      },
      body: l,
      method: 'POST',
    })
    const data = await response.json()
    return data
  }

  const visitUserMessages = async (username: string) => {
    logNow('visitUserMessages called', { username, currentUrl: window.location.href })
    
    // Wait for page to fully load
    await delay(3000);
    
    let message_button: HTMLElement | null = null;
    
    // Extended selectors for different button types
    const buttonSelectors = [
      'div[role="button"]',
      'button',
      'a[role="button"]', 
      'header button',
      'header div[role="button"]',
      'article button',
      'article div[role="button"]',
      '[data-testid*="message"]',
      '[data-testid*="send"]'
    ];
    
    const allButtons: HTMLElement[] = [];
    for (const selector of buttonSelectors) {
      const elements = document.querySelectorAll<HTMLElement>(selector);
      allButtons.push(...Array.from(elements));
    }
    
    // Remove duplicates
    const uniqueButtons = Array.from(new Set(allButtons));
    logNow(`Found ${uniqueButtons.length} potential buttons to check for "Message" text.`);

    // Log all button texts for debugging with more details
    const buttonTexts = uniqueButtons.map(btn => ({
      text: btn.innerText.trim(),
      ariaLabel: btn.getAttribute('aria-label'),
      title: btn.getAttribute('title'),
      className: btn.className,
      tagName: btn.tagName,
      testId: btn.getAttribute('data-testid')
    })).filter(btn => btn.text.length > 0 || btn.ariaLabel || btn.testId);
    
    logNow('All button details found:', buttonTexts);

    // Try multiple text patterns for different languages
    const messagePatterns = [
      'message',
      'wiadomość', 
      'napisz',
      'send message',
      'wyślij wiadomość',
      'dm',
      'direct',
      'chat'
    ];

    for (const btn of uniqueButtons) {
      const buttonText = btn.innerText.toLowerCase().trim();
      const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
      const title = (btn.getAttribute('title') || '').toLowerCase();
      const testId = (btn.getAttribute('data-testid') || '').toLowerCase();
      
      // Check all text sources for message patterns
      const allTexts = [buttonText, ariaLabel, title, testId].join(' ');
      
      for (const pattern of messagePatterns) {
        if (allTexts.includes(pattern)) {
          message_button = btn;
          logNow('Found message button by pattern:', { 
            pattern, 
            buttonText, 
            ariaLabel, 
            title, 
            testId,
            element: btn 
          });
          break;
        }
      }
      
      if (message_button) break;
    }

    if (message_button) {
      await delay(1000);
      logNow('visitUserMessages: Attempting to click message_button:', message_button);
      
      // Try multiple click methods
      try {
        // Method 1: Regular click
        message_button.click();
        await delay(2000);
        
        // Check if navigation happened
        if (window.location.href.includes('/direct/')) {
          logNow('✅ Successfully navigated to DM page via regular click');
        } else {
          // Method 2: Dispatch click event
          logNow('Regular click failed, trying event dispatch');
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
          });
          message_button.dispatchEvent(clickEvent);
          await delay(2000);
          
          if (window.location.href.includes('/direct/')) {
            logNow('✅ Successfully navigated to DM page via event dispatch');
          } else {
            // Method 3: Focus and Enter key
            logNow('Event dispatch failed, trying focus + Enter');
            message_button.focus();
            await delay(500);
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              code: 'Enter',
              keyCode: 13,
              which: 13,
              bubbles: true
            });
            message_button.dispatchEvent(enterEvent);
            await delay(2000);
            
            if (window.location.href.includes('/direct/')) {
              logNow('✅ Successfully navigated to DM page via Enter key');
            } else {
              logNow('❌ All click methods failed - DM page not opened');
            }
          }
        }
      } catch (error) {
        logNow('❌ Error clicking message button:', error);
      }
      
      logNow('visitUserMessages: After message button interaction', { finalUrl: window.location.href });
    } else {
      logNow('❌ visitUserMessages: Direct message button not found. Trying three-dots menu approach...');
      
      // Look for three-dots menu (Options) button
      let optionsButton: HTMLElement | null = null;
      
      // Look for options button by aria-label and SVG content
      for (const btn of uniqueButtons) {
        const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
        const svgTitle = btn.querySelector('svg title')?.textContent?.toLowerCase() || '';
        
        if (ariaLabel.includes('opcje') || ariaLabel.includes('options') || 
            svgTitle.includes('opcje') || svgTitle.includes('options')) {
          optionsButton = btn;
          logNow('Found options button:', { ariaLabel, svgTitle, element: btn });
          break;
        }
      }
      
      if (optionsButton) {
        logNow('📱 Clicking options button to reveal message option...');
        try {
          optionsButton.click();
          await delay(2000); // Wait for menu to appear
          
          // Now look for the message button in the dropdown menu
          const newButtons = document.querySelectorAll<HTMLElement>('button, div[role="button"]');
          let messageButtonInMenu: HTMLElement | null = null;
          
          for (const btn of newButtons) {
            const buttonText = btn.innerText.toLowerCase().trim();
            // Exact match for Polish "Wyślij wiadomość" from the HTML you provided
            if (buttonText === 'wyślij wiadomość' || 
                buttonText.includes('send message') || 
                buttonText.includes('message') || 
                buttonText === 'wiadomość') {
              messageButtonInMenu = btn;
              logNow('Found message button in dropdown menu:', { 
                text: buttonText, 
                exactText: btn.innerText,
                element: btn 
              });
              break;
            }
          }
          
          if (messageButtonInMenu) {
            logNow('📱 Clicking message button from dropdown menu...');
            messageButtonInMenu.click();
            // Wait for navigation with retry logic
            let navigationSuccess = false;
            for (let attempt = 1; attempt <= 3; attempt++) {
              await delay(3000);
              
              if (window.location.href.includes('/direct/')) {
                logNow('✅ Successfully navigated to DM page via dropdown menu');
                navigationSuccess = true;
                break;
              } else {
                logNow(`❌ Attempt ${attempt}/3 - Dropdown menu click failed, URL: ${window.location.href}`);
                
                if (attempt < 3) {
                  // Try alternative approach: look for direct "Message" button that might have appeared
                  logNow('🔄 Retrying with direct message button approach...');
                  
                  const directMessageBtn = document.querySelector('div[aria-label*="Message"], div[aria-label*="Wiadomość"], a[aria-label*="Message"], a[aria-label*="Wiadomość"]');
                  if (directMessageBtn) {
                    logNow('Found direct message button, clicking...');
                    (directMessageBtn as HTMLElement).click();
                  } else {
                    // Try clicking the message button from dropdown again
                    // Find button by text content instead of CSS selector
                    const allButtons = document.querySelectorAll('button, div[role="button"]');
                    const retryMessageBtn = Array.from(allButtons).find(btn => {
                      const text = btn.textContent?.toLowerCase() || '';
                      return text.includes('message') || text.includes('wiadomość') || text.includes('wyślij wiadomość');
                    });
                    if (retryMessageBtn) {
                      logNow('🔄 Retrying with found message button:', retryMessageBtn.textContent);
                      (retryMessageBtn as HTMLElement).click();
                    }
                  }
                }
              }
            }
            
            if (!navigationSuccess) {
              logNow('❌ Failed to navigate to DM page after 3 attempts via dropdown menu');
              // Don't throw error, continue to let the background script handle it
            }
          } else {
            logNow('❌ Message button not found in dropdown menu');
            
            // Log what buttons are available in the menu
            const menuButtons = Array.from(newButtons).map(btn => ({
              text: btn.innerText.trim(),
              ariaLabel: btn.getAttribute('aria-label'),
              className: btn.className
            })).filter(btn => btn.text.length > 0);
            
            logNow('Available buttons in dropdown menu:', menuButtons);
          }
        } catch (error) {
          logNow('❌ Error interacting with options menu:', error);
        }
      } else {
        logNow('❌ Options button not found either. Available button details:', buttonTexts);
        
        // Try to find any button that might be the message button based on position/styling
        const potentialButtons = uniqueButtons.filter(btn => {
          const rect = btn.getBoundingClientRect();
          return rect.width > 50 && rect.height > 25; // Filter out tiny buttons
        });
        
        logNow('Potential buttons by size filtering:', potentialButtons.map(btn => ({
          text: btn.innerText.trim(),
          ariaLabel: btn.getAttribute('aria-label'),
          className: btn.className,
          size: { 
            width: btn.getBoundingClientRect().width, 
            height: btn.getBoundingClientRect().height 
          }
        })));
        
        // Also check for SVG elements that might indicate options
        const svgElements = document.querySelectorAll('svg[aria-label], svg title');
        logNow('Available SVG elements with labels:', Array.from(svgElements).map(svg => ({
          ariaLabel: svg.getAttribute('aria-label'),
          title: svg.querySelector('title')?.textContent,
          parentButton: svg.closest('button, div[role="button"]')?.innerText
        })));
      }
    }
    
    logNow('visitUserMessages completed', { username, finalUrl: window.location.href });
  }

  const resolveUsernameToUserId = async (username: string): Promise<string | null> => {
    try {
      logNow('Attempting to resolve username to user ID:', username);
      const profileUrl = `https://www.instagram.com/api/v1/users/web_profile_info/?username=${username}`;
      const response = await fetch(profileUrl, {
        credentials: 'include',
        headers: {
          'User-Agent': navigator.userAgent,
          'Accept': 'application/json',
          'x-ig-app-id': '936619743392459'
        }
      });
      if (response.ok) {
        const json = await response.json();
        const userId = json?.data?.user?.id;
        if (userId) {
          logNow(`Resolved ${username} to user ID: ${userId}`);
          return userId;
        }
      }
      logNow('Could not resolve username to user ID via web_profile_info API');
      return null;
    } catch (error) {
      logNow('Error in resolveUsernameToUserId:', error);
      return null;
    }
  }

  const sendMessage = async (username: string, message_text: string) => {
    logNow('sendMessage called', { username, message_text, currentUrl: window.location.href })

    try {
      const recipient_id = await resolveUsernameToUserId(username);
      if (!recipient_id) {
        logNow('❌ Failed to resolve username to user ID:', username);
        throw new Error(`Failed to resolve username '${username}' to a user ID.`);
      }
      logNow('✅ Resolved username to user ID:', { username, recipient_id });

      let viewerId = 'unknown';
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'ds_user_id' && value) {
          viewerId = value;
          break;
        }
      }
      logNow('Viewer ID from cookies:', viewerId);

      const urlMatch = window.location.pathname.match(/\/direct\/t\/(\d+)/);
      let threadId = urlMatch ? urlMatch[1] : 'unknown';
      logNow('Current URL analysis:', { 
        pathname: window.location.pathname, 
        urlMatch: urlMatch ? urlMatch[0] : null, 
        threadId 
      });
      
      if (threadId === 'unknown') {
          logNow('No existing thread found, creating new one for recipient:', recipient_id)
          const group_thread_resp = await createGroupThread(recipient_id)

          if (group_thread_resp && group_thread_resp.status === 'ok') {
            threadId = group_thread_resp.thread_id;
            logNow('✅ New thread created successfully:', threadId);
            
            // 🔄 NOTIFY BACKGROUND: Request navigation to DM conversation page
            logNow('🔄 Requesting navigation to DM conversation...');
            try {
              // Send message to background script to handle navigation
              const dmUrl = `https://www.instagram.com/direct/t/${threadId}/`;
              
              // Instead of navigating immediately, wait a moment for thread to be fully created
              await delay(1000);
              logNow('✅ Thread creation completed, ready for messaging');
              
            } catch (error) {
              logNow('⚠️ Thread creation post-processing failed:', error);
            }
          } else {
            logNow('❌ Failed to create new thread:', group_thread_resp);
          }
      } else {
        logNow('✅ Using existing thread:', threadId);
      }

      const messagePromise = new Promise((resolve, reject) => {
        const messageHandler = (event: MessageEvent) => {
          if (event.data && event.data.type === 'INJECT_DISPATCH_DM_RESPONSE') {
            window.removeEventListener('message', messageHandler)
            if (event.data.ret === 1) {
              logNow('Message sent successfully!')
              resolve(true)
            } else {
              logNow('Message failed:', event.data)
              reject(new Error(`Message failed: ${event.data.error || event.data.status_code}`))
            }
          }
        }

        window.addEventListener('message', messageHandler)

        window.postMessage({
          type: 'INJECT_DISPATCH_DM_REQUEST',
          thread_id: threadId,
          viewer_id: viewerId,
          user: { id: recipient_id, username: username },
          text: message_text,
          debug: false
        }, '*')

        setTimeout(() => {
          window.removeEventListener('message', messageHandler)
          reject(new Error('Message timeout'))
        }, 15000)
      })

      return await messagePromise
    } catch (error) {
      logNow('Error in sendMessage:', error)
      console.error('Error in sendMessage:', error)
      return false
    }
  }

  useEffect(() => {
    Browser.runtime.onMessage.addListener(async (message: any) => {
      console.log('Message from bg :: ', message)
      if (message.type === MessageTypes.MESSAGE_PROFILE) {
        return await sendMessage(message.data.recipent_id, message.data.text)
      } else if (message.type === MessageTypes.VISIT_USER_MESSAGES) {
        return await visitUserMessages(message.data.recipent_username)
      } else if (message.type === MessageTypes.START_PROCESS) {
        botRunning.current = true
      } else if (message.type === MessageTypes.STOP_PROCESS) {
        botRunning.current = false
      } else if (message.type === MessageTypes.GET_INSTA_INBOX) {
        return await getInstagramStories(message.data.config)
      } else if (message.type === MessageTypes.GET_PROFILE_URL) {
        return await getProfileUrl()
      } else if (message.type === MessageTypes.OPEN_FOLLOWERS_PANEL) {
        return await openFollowersPanel(message.data.username)
      } else if (message.type === MessageTypes.GET_FOLLOWERS_LIST_BY_COUNT) {
        return await getFollowersList(message.data.count, message.data.skipCount || 0)
      } else if (message.type === MessageTypes.GET_INITIAL_FOLLOWERS) {
        // Dedicated method for initial scraping - always gets 250 most recent followers
        return await getFollowersList(250, 0)
      }
    })
  }, [])

  return <></>
}

export default Component
