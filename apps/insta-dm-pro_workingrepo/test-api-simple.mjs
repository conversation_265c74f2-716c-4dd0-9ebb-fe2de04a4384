#!/usr/bin/env node

/**
 * Simple API test script for Chrome extension
 * Tests the API connections directly with fetch
 */

const API_KEY = 'api_f3b1b6854faa2f054951d971c8e54e4f';
const BASE_URL = 'https://3e76-2a02-a31a-c2e5-af00-789e-f28a-1bad-e8c3.ngrok-free.app';

function getHeaders(apiKey) {
  return {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'true',
    'X-API-Key': apiKey
  };
}

async function testApiEndpoints() {
  console.log('🧪 Testing AISetter Chrome Extension API...\n');
  
  try {
    // Test 1: Verify API key
    console.log('1️⃣ Testing API key verification...');
    const keyResponse = await fetch(`${BASE_URL}/api/verify-api-key`, {
      method: 'POST',
      headers: getHeaders(API_KEY)
    });
    const keyResult = await keyResponse.json();
    console.log('Status:', keyResponse.status);
    console.log('Result:', keyResult);
    
    if (!keyResult.success) {
      console.log('❌ API key verification failed.');
      return;
    }
    
    console.log('✅ API key verified successfully\n');
    
    // Test 2: Get Chrome extension settings
    console.log('2️⃣ Testing Chrome extension settings...');
    const settingsResponse = await fetch(`${BASE_URL}/api/chrome-extension/settings`, {
      method: 'GET',
      headers: getHeaders(API_KEY)
    });
    const settingsResult = await settingsResponse.json();
    console.log('Status:', settingsResponse.status);
    console.log('Result:', settingsResult);
    console.log(settingsResult.success ? '✅ Settings retrieved' : '❌ Settings failed');
    console.log('');
    
    // Test 3: Get attack list
    console.log('3️⃣ Testing attack list...');
    const attackResponse = await fetch(`${BASE_URL}/api/chrome-extension/attack-list?limit=5`, {
      method: 'GET',
      headers: getHeaders(API_KEY)
    });
    const attackResult = await attackResponse.json();
    console.log('Status:', attackResponse.status);
    console.log('Result:', attackResult);
    console.log(attackResult.success ? '✅ Attack list retrieved' : '❌ Attack list failed');
    console.log('');
    
    // Test 4: Get pending follow-ups
    console.log('4️⃣ Testing pending follow-ups...');
    const followUpsResponse = await fetch(`${BASE_URL}/api/pending-follow-ups`, {
      method: 'GET',
      headers: getHeaders(API_KEY)
    });
    const followUpsResult = await followUpsResponse.json();
    console.log('Status:', followUpsResponse.status);
    console.log('Result:', followUpsResult);
    console.log(followUpsResult.success ? '✅ Follow-ups retrieved' : '❌ Follow-ups failed');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

testApiEndpoints();