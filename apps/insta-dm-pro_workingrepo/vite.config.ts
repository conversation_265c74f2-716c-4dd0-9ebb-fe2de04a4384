import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [
    react()
  ],
  publicDir: 'public',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@background': path.resolve(__dirname, './src/background'),
      '@content': path.resolve(__dirname, './src/content'),
      '@popup': path.resolve(__dirname, './src/popup'),
      '@shared': path.resolve(__dirname, './src/shared'),
      '@types': path.resolve(__dirname, './src/types')
    }
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        popup: path.resolve(__dirname, 'index.html'),
        background: path.resolve(__dirname, 'src/background/index.ts'),
        content: path.resolve(__dirname, 'src/content/index.tsx')
      },
      output: {
        entryFileNames: (chunkInfo) => {
          return '[name].js'
        },
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            if (assetInfo.name.includes('content')) return 'content.css'
            return 'index.css'
          }
          return 'assets/[name][extname]'
        },
        format: 'es',
        // Ensure content script dependencies are bundled together
        manualChunks: (id) => {
          // Bundle content script and its dependencies together
          if (id.includes('src/content/') ||
              id.includes('src/shared/messaging') ||
              id.includes('src/shared/delay') ||
              id.includes('preact') ||
              id.includes('react')) {
            return 'content'
          }
          // Keep background script separate
          if (id.includes('src/background/')) {
            return 'background'
          }
          return undefined
        }
      }
    }
  },
  define: {
    global: 'globalThis'
  }
})