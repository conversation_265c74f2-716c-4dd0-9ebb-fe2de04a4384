1. Wtyczka przy pierwszym odpaleniu pobiera API i sprawdza czy status jest FRESH START.
2. <PERSON><PERSON><PERSON> jest to odpala INITIAL SCRAPING (250 ostatnich followersów i wrzuca ich do listy Instagram Followers).
3. Aplikacja wtedy pobiera ID oraz nazwy wszystkich odbytych konwersacji do listy: Instagram Conversations Not Gathered (to może potrwać długo)
4. Po pobraniu listy aplikacja ustala gdzieś w bazie danych "CONVERSATION_GATHERING_DONE".
5. W momencie gdy CONVERSATION_GATHERING_DONE zaczyna porównywać jeden po jednym followersie sprawdzając czy miała z nim konwersacje.
6. Je<PERSON><PERSON> miała to przełącza na tryb CONVERSATION GATHERING i wrzuca follow upy na listę "Attack List".
7. <PERSON><PERSON><PERSON> nie miała to wrzuca na listę Attack List z predefiniowanymi wiadomościami.
8. Wtyczka pobiera listę attack list i zaczyna pisać wiadomości respektując czasy: Time between DMs (minutes), How many DMs before break, How much break should take? (minutes), Natural pause start.
9. Wtyczka zaczyna wysyłać wiadomości.
10. Po wysłaniu wiadomości wtyczka sprawdza czy pojawili się nowi followersi.
11. Jeżeli pojawili się nowi followersi to system sprawdza czy miał konwersacje z danym followersem i wrzuca na attack listę.
12. Jeżeli wtyczka ma ustawione Focus on new followers to piszę wpierw do ludzi z priorytetem 3 którzy pojawili się najszybciej na liście.
13. Ludzie normalnie odpisują a system ustala: System followupy (do 24h ale dla bezpieczeństwa 23h i 55m od ostatniej wiadomości użytkownika), a jeżeli są później to wrzuca kolejne na Attack Liste.
14. Wtyczka piszę według priorytetów 5,4,3,2,1 jeżeli nie ma ustawionego że ma skupiać się na nowych followersach.
15. W momencie gdy system da sygnał START SCRAPING to wtyczka ponownie włącza scrapowanie followersów od ostatniego sprawdzonego a następnie wrzuca na attack listę. (Dla bezpieczeństwa w razie jakby odfollołował sprawdzamy 10 ostatnich.) Więc scrapuje 250-500. W momencie gdy pojawi się kolejny sygnał to sprawdza 500-750 itp. 
16. Gdy wtyczka zostanie wyłączona, wpierw sprawdza czy ma initial scraping, jeżeli nie to pierwszym jej zadaniem jest wrzucenie do instagram followers followersów którzy się pojawili gdy wtyczka była wyłączona.
