import { cache } from 'react';

import { prisma } from '@workspace/database/client';

import type { TestAdminPromptDto } from '~/types/dtos/test-admin-prompt-dto';

export const getTestAdminPrompt = cache(
  async (organizationId: string, userId: string): Promise<TestAdminPromptDto | null> => {
    const testAdminPrompt = await prisma.testAdminPrompt.findFirst({
      where: {
        organizationId,
        userId
      },
      select: {
        id: true,
        organizationId: true,
        userId: true,
        generalPrompt: true,
        technicalPrompt: true,
        conversationGatheringPrompt: true
      }
    });

    if (!testAdminPrompt) {
      return null;
    }

    return {
      id: testAdminPrompt.id,
      organizationId: testAdminPrompt.organizationId,
      userId: testAdminPrompt.userId,
      generalPrompt: testAdminPrompt.generalPrompt,
      technicalPrompt: testAdminPrompt.technicalPrompt,
      conversationGatheringPrompt: testAdminPrompt.conversationGatheringPrompt ?? undefined
    };
  }
);
