'use client';

import * as React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight } from 'lucide-react';

import { baseUrl, getPathname } from '@workspace/routes';
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  type SidebarGroupProps
} from '@workspace/ui/components/sidebar';
import { cn } from '@workspace/ui/lib/utils';

import { createMainNavItems, type NavItem } from '~/components/organizations/slug/nav-items';
import { useActiveOrganization } from '~/hooks/use-active-organization';
import { useAuthContext } from '~/hooks/use-auth-context';

export function NavMain(props: SidebarGroupProps): React.JSX.Element {
  const pathname = usePathname();
  const activeOrganization = useActiveOrganization();
  const { session } = useAuthContext();

  // Only SaaS admin (<EMAIL>) gets admin access
  const isSaasAdmin = session?.user?.email === '<EMAIL>';

  // Get all main nav items including Instagram items
  const mainNavItems = createMainNavItems(activeOrganization.slug, false, isSaasAdmin);

  return (
    <SidebarGroup {...props}>
      <SidebarMenu>
        {mainNavItems.map((item, index) => {
          const isActive = pathname.startsWith(
            getPathname(item.href, baseUrl.Dashboard)
          );

          // Check if any sub-item is active
          const isSubItemActive = item.items?.some(subItem =>
            pathname.startsWith(getPathname(subItem.href, baseUrl.Dashboard))
          );

          const hasActiveChild = isActive || isSubItemActive;

          return (
            <SidebarMenuItem key={index}>
              {item.items ? (
                // Collapsible group item
                <CollapsibleNavItem
                  item={item}
                  isActive={hasActiveChild || false}
                  pathname={pathname}
                />
              ) : (
                // Regular nav item
                <SidebarMenuButton
                  asChild
                  isActive={isActive}
                  tooltip={item.title}
                >
                  <Link
                    href={item.disabled ? '~/' : item.href}
                    target={item.external ? '_blank' : undefined}
                  >
                    <item.icon
                      className={cn(
                        'size-4 shrink-0',
                        isActive ? 'text-foreground' : 'text-muted-foreground'
                      )}
                    />
                    <span
                      className={
                        isActive
                          ? 'dark:text-foreground'
                          : 'dark:text-muted-foreground'
                      }
                    >
                      {item.title}
                    </span>
                  </Link>
                </SidebarMenuButton>
              )}
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}

interface CollapsibleNavItemProps {
  item: NavItem;
  isActive: boolean;
  pathname: string;
}

function CollapsibleNavItem({ item, isActive, pathname }: CollapsibleNavItemProps): React.JSX.Element {
  const [isOpen, setIsOpen] = React.useState(isActive);

  // Auto-expand if any child is active
  React.useEffect(() => {
    if (isActive) {
      setIsOpen(true);
    }
  }, [isActive]);

  return (
    <>
      <SidebarMenuButton
        onClick={() => setIsOpen(!isOpen)}
        isActive={isActive}
        tooltip={item.title}
      >
        <item.icon
          className={cn(
            'size-4 shrink-0',
            isActive ? 'text-foreground' : 'text-muted-foreground'
          )}
        />
        <span
          className={
            isActive
              ? 'dark:text-foreground'
              : 'dark:text-muted-foreground'
          }
        >
          {item.title}
        </span>
        <ChevronRight
          className={cn(
            'ml-auto size-4 shrink-0 transition-transform duration-200',
            isOpen && 'rotate-90'
          )}
        />
      </SidebarMenuButton>
      {isOpen && item.items && (
        <SidebarMenuSub>
          {item.items.map((subItem, subIndex) => {
            const isSubActive = pathname.startsWith(
              getPathname(subItem.href, baseUrl.Dashboard)
            );
            return (
              <SidebarMenuSubItem key={subIndex}>
                <SidebarMenuSubButton
                  asChild
                  isActive={isSubActive}
                >
                  <Link
                    href={subItem.disabled ? '~/' : subItem.href}
                    target={subItem.external ? '_blank' : undefined}
                  >
                    <subItem.icon className="size-4 shrink-0" />
                    <span>{subItem.title}</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            );
          })}
        </SidebarMenuSub>
      )}
    </>
  );
}
