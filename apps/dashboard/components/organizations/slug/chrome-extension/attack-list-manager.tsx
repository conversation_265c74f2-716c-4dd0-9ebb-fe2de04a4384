'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar, MoreHorizontal, RefreshCw, Trash2, Filter, Eye, Edit, Search, X, Send } from 'lucide-react';

import { But<PERSON> } from '@workspace/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@workspace/ui/components/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@workspace/ui/components/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@workspace/ui/components/dropdown-menu';
import { Badge } from '@workspace/ui/components/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@workspace/ui/components/avatar';
import { Input } from '@workspace/ui/components/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select';
import { Checkbox } from '@workspace/ui/components/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog';
import { Textarea } from '@workspace/ui/components/textarea';
import { useToast } from '@workspace/ui/hooks/use-toast';

interface AttackListItem {
  id: string;
  instagramId: string | null;
  username: string;
  avatar: string | null;
  priority: number;
  status: string;
  attackListStatus: string | null;
  nextMessageAt: Date | null;
  lastInteractionAt: Date | null;
  stage: string;
  conversationSource: string;
  createdAt: Date;
  updatedAt: Date;
  // Message tracking fields
  messageStatus?: {
    sent: number;
    total: number;
    currentSequence: number;
  };
  plannedMessages?: {
    type: 'ai_followups' | 'batch_messages';
    messages: Array<{
      id?: string;
      sequenceNumber: number;
      message: string;
      scheduledTime?: Date;
      status?: string;
      delayMinutes?: number;
    }>;
  };
}

const priorityLabels = {
  5: 'Highly Engaged',
  4: 'Engaged',
  3: 'New Followers',
  2: 'Low Engaged',
  1: 'Follow-up'
};

const statusColors: Record<string, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  ready: 'bg-green-100 text-green-800',
  in_progress: 'bg-blue-100 text-blue-800',
  completed: 'bg-gray-100 text-gray-800',
  failed: 'bg-red-100 text-red-800',
  new: 'bg-blue-100 text-blue-800',
  initial: 'bg-purple-100 text-purple-800',
  engaged: 'bg-green-100 text-green-800'
};

export function AttackListManager(): React.JSX.Element {
  const { toast } = useToast();
  const [attackList, setAttackList] = React.useState<AttackListItem[]>([]);
  const [filteredAttackList, setFilteredAttackList] = React.useState<AttackListItem[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [metadata, setMetadata] = React.useState<any>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = React.useState('');
  const [priorityFilter, setPriorityFilter] = React.useState<string>('all');
  const [stageFilter, setStageFilter] = React.useState<string>('all');

  // Selection states
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = React.useState(false);

  // Dialog states
  const [showMessagesDialog, setShowMessagesDialog] = React.useState(false);
  const [showPriorityDialog, setShowPriorityDialog] = React.useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const [selectedContact, setSelectedContact] = React.useState<AttackListItem | null>(null);
  const [newPriority, setNewPriority] = React.useState<number>(3);

  // Load attack list data
  React.useEffect(() => {
    loadAttackList();
  }, []);

  // Filter attack list when filters change
  React.useEffect(() => {
    let filtered = attackList;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.username.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Priority filter
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(item => item.priority === parseInt(priorityFilter));
    }

    // Stage filter
    if (stageFilter !== 'all') {
      filtered = filtered.filter(item => item.stage === stageFilter);
    }

    setFilteredAttackList(filtered);
  }, [attackList, searchTerm, priorityFilter, stageFilter]);

  const loadAttackList = async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/chrome-extension/attack-list?limit=50');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to load attack list');
      }

      // Convert date strings back to Date objects
      const attackListData = result.data.map((item: any) => ({
        ...item,
        nextMessageAt: item.nextMessageAt ? new Date(item.nextMessageAt) : null,
        lastInteractionAt: item.lastInteractionAt ? new Date(item.lastInteractionAt) : null,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      }));

      setAttackList(attackListData);
      setMetadata(result.metadata);
    } catch (error) {
      console.error('Error loading attack list:', error);
      toast({
        title: 'Error',
        description: 'Failed to load attack list',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const removeFromAttackList = async (id: string) => {
    try {
      const response = await fetch(`/api/chrome-extension/attack-list?id=${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: 'completed' })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to update attack list item');
      }

      // Remove from local state
      setAttackList(prev => prev.filter(item => item.id !== id));

      toast({
        title: 'Success',
        description: 'User removed from attack list'
      });
    } catch (error) {
      console.error('Error removing from attack list:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove user from attack list',
        variant: 'destructive'
      });
    }
  };

  const refreshAttackList = () => {
    loadAttackList();
  };

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedItems(new Set(filteredAttackList.map(item => item.id)));
    } else {
      setSelectedItems(new Set());
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedItems(newSelected);
    setSelectAll(newSelected.size === filteredAttackList.length);
  };

  // Bulk operations
  const handleBulkDelete = async () => {
    try {
      setIsLoading(true);
      const promises = Array.from(selectedItems).map(id =>
        fetch(`/api/chrome-extension/attack-list?id=${id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status: 'completed' })
        })
      );

      await Promise.all(promises);

      setAttackList(prev => prev.filter(item => !selectedItems.has(item.id)));
      setSelectedItems(new Set());
      setSelectAll(false);
      setShowDeleteDialog(false);

      toast({
        title: 'Success',
        description: `Removed ${selectedItems.size} users from attack list`
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to remove users from attack list',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAll = async () => {
    try {
      setIsLoading(true);
      const promises = attackList.map(item =>
        fetch(`/api/chrome-extension/attack-list?id=${item.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status: 'completed' })
        })
      );

      await Promise.all(promises);

      setAttackList([]);
      setSelectedItems(new Set());
      setSelectAll(false);

      toast({
        title: 'Success',
        description: 'Cleared entire attack list'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to clear attack list',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChangePriority = async () => {
    if (!selectedContact) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/chrome-extension/attack-list?id=${selectedContact.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ priority: newPriority })
      });

      if (!response.ok) throw new Error('Failed to update priority');

      setAttackList(prev => prev.map(item =>
        item.id === selectedContact.id ? { ...item, priority: newPriority } : item
      ));
      setShowPriorityDialog(false);
      setSelectedContact(null);

      toast({
        title: 'Success',
        description: 'Priority updated successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update priority',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestMarkSent = async (contactId: string, username: string) => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/test/mark-message-sent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ contactId, messageSequence: 1 })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to mark message as sent');
      }

      // Refresh the attack list to see updated status
      await loadAttackList();

      toast({
        title: 'Test Success',
        description: `Message marked as sent for @${username}`
      });
    } catch (error) {
      toast({
        title: 'Test Error',
        description: 'Failed to mark message as sent',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Attack List</CardTitle>
              <CardDescription>
                {metadata?.description || 'Followers queued for messaging by the Chrome Extension'}
                {filteredAttackList.length !== attackList.length && (
                  <span className="ml-2 text-sm">
                    (Showing {filteredAttackList.length} of {attackList.length})
                  </span>
                )}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              {selectedItems.size > 0 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDeleteDialog(true)}
                    disabled={isLoading}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Selected ({selectedItems.size})
                  </Button>
                </>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeleteAll}
                disabled={isLoading || attackList.length === 0}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshAttackList}
                disabled={isLoading}
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 pt-4 border-t">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48"
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchTerm('')}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="5">5 - Highly Engaged</SelectItem>
                  <SelectItem value="4">4 - Engaged</SelectItem>
                  <SelectItem value="3">3 - New Followers</SelectItem>
                  <SelectItem value="2">2 - Low Engaged</SelectItem>
                  <SelectItem value="1">1 - Follow-up</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Select value={stageFilter} onValueChange={setStageFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Stage" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Stages</SelectItem>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="initial">Initial</SelectItem>
                  <SelectItem value="engaged">Engaged</SelectItem>
                  <SelectItem value="qualified">Qualified</SelectItem>
                  <SelectItem value="formsent">Form Sent</SelectItem>
                  <SelectItem value="disqualified">Disqualified</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {metadata && metadata.priorityBreakdown && (
            <div className="mb-4 p-4 bg-muted/50 rounded-lg">
              <div className="text-sm text-muted-foreground">
                <strong>Total contacts ready:</strong> {metadata.readyForMessaging} out of {metadata.totalInDatabase}
              </div>
              <div className="flex gap-4 mt-2 text-xs">
                {Object.entries(metadata.priorityBreakdown).map(([priority, count]) => (
                  <div key={priority}>
                    <Badge variant="outline" className="mr-1">
                      {priority.replace('priority', 'P')}
                    </Badge>
                    {count as number}
                  </div>
                ))}
              </div>
            </div>
          )}
          {filteredAttackList.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {attackList.length === 0
                ? "No users in attack list. Users will appear here when added by the Chrome Extension."
                : "No users match the current filters."
              }
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">
                    <Checkbox
                      checked={selectAll}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all"
                    />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Stage</TableHead>
                  <TableHead>Attack Status</TableHead>
                  <TableHead>Message Status</TableHead>
                  <TableHead>Next Message</TableHead>
                  <TableHead>Last Interaction</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAttackList.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedItems.has(item.id)}
                        onCheckedChange={(checked) => handleSelectItem(item.id, checked as boolean)}
                        aria-label={`Select ${item.username}`}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={item.avatar || undefined} />
                          <AvatarFallback>
                            {item.username.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">@{item.username}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {item.priority} - {priorityLabels[item.priority as keyof typeof priorityLabels]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusColors[item.stage] || 'bg-gray-100 text-gray-800'}>
                        {item.stage}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusColors[item.attackListStatus || 'pending'] || 'bg-yellow-100 text-yellow-800'}>
                        {item.attackListStatus || 'pending'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {item.messageStatus ? (
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {item.messageStatus.sent}/{item.messageStatus.total}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            messages sent
                          </span>
                        </div>
                      ) : (
                        <span className="text-xs text-muted-foreground">No messages</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {item.nextMessageAt ? format(item.nextMessageAt, 'MMM dd, yyyy HH:mm') : 'Ready now'}
                    </TableCell>
                    <TableCell>
                      {item.lastInteractionAt ? format(item.lastInteractionAt, 'MMM dd, yyyy HH:mm') : 'Never'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedContact(item);
                            setShowMessagesDialog(true);
                          }}
                          title="View planned messages"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedContact(item);
                                setNewPriority(item.priority);
                                setShowPriorityDialog(true);
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Change Priority
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleTestMarkSent(item.id, item.username)}
                              className="text-blue-600"
                            >
                              <Send className="mr-2 h-4 w-4" />
                              Test: Mark Message Sent
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => removeFromAttackList(item.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Remove
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Messages Dialog */}
      <Dialog open={showMessagesDialog} onOpenChange={setShowMessagesDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Planned Messages for @{selectedContact?.username}</DialogTitle>
            <DialogDescription>
              View the messages that will be sent to this contact
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {selectedContact && (
              <div className="space-y-3">
                <div className="p-4 bg-muted/50 rounded-lg">
                  <div className="text-sm text-muted-foreground mb-2">Contact Info</div>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={selectedContact.avatar || undefined} />
                      <AvatarFallback>
                        {selectedContact.username.slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">@{selectedContact.username}</div>
                      <div className="text-sm text-muted-foreground">
                        Priority: {selectedContact.priority} - {priorityLabels[selectedContact.priority as keyof typeof priorityLabels]}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Stage: {selectedContact.stage}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Planned Messages:</div>
                  {selectedContact.plannedMessages ? (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {selectedContact.plannedMessages.type === 'ai_followups' ? 'AI Follow-ups' : 'Batch Messages'}
                          </Badge>
                          <span className="text-muted-foreground">
                            {selectedContact.plannedMessages.messages.length} message(s) planned
                          </span>
                        </div>
                        {selectedContact.messageStatus && (
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              Progress: {selectedContact.messageStatus.sent}/{selectedContact.messageStatus.total}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              messages sent
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        {selectedContact.plannedMessages.messages.map((message, index) => {
                          // Determine message status based on sequence and tracking
                          const messageStatus = selectedContact.messageStatus;
                          const isMessageSent = messageStatus && message.sequenceNumber <= messageStatus.sent;

                          // For batch messages, all messages should be "To Send" when none have been sent
                          // For AI follow-ups, use the original logic
                          let isMessageToSend = false;
                          if (selectedContact.plannedMessages?.type === 'batch_messages') {
                            // For batch messages: all messages are "To Send" if none have been sent yet
                            isMessageToSend = !isMessageSent && !!messageStatus && messageStatus.sent === 0;
                          } else {
                            // For AI follow-ups: use sequence-based logic
                            isMessageToSend = !isMessageSent && !!messageStatus && message.sequenceNumber <= (messageStatus.currentSequence ?? 0) + 1;
                          }

                          return (
                            <div key={index} className="p-3 bg-muted/30 rounded-lg border">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <div className="text-sm font-medium">
                                    {selectedContact.plannedMessages?.type === 'ai_followups'
                                      ? `Follow Up ${message.sequenceNumber - 1}`
                                      : `Message ${message.sequenceNumber}`}
                                  </div>
                                  {/* Message Status Badge */}
                                  {isMessageSent ? (
                                    <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                                      Sent
                                    </Badge>
                                  ) : isMessageToSend ? (
                                    <Badge variant="default" className="text-xs bg-yellow-100 text-yellow-800">
                                      To Send
                                    </Badge>
                                  ) : (
                                    <Badge variant="outline" className="text-xs">
                                      Pending
                                    </Badge>
                                  )}
                                </div>
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                  {selectedContact.plannedMessages?.type === 'ai_followups' ? (
                                    <>
                                      {message.scheduledTime && (
                                        <span>Scheduled: {format(new Date(message.scheduledTime), 'MMM dd, yyyy HH:mm')}</span>
                                      )}
                                      {message.status && (
                                        <Badge variant="outline" className="text-xs">
                                          {message.status}
                                        </Badge>
                                      )}
                                    </>
                                  ) : (
                                    // For batch messages, show sequence info (Chrome extension handles 20-40s delays)
                                    <span>
                                      Batch message {message.sequenceNumber}
                                      {message.sequenceNumber === 1 ? ' (immediate)' : ' (after 20-40s)'}
                                    </span>
                                  )}
                                </div>
                              </div>
                              <div className="text-sm bg-background p-2 rounded border">
                                {message.message}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 bg-muted/30 rounded-lg">
                      <div className="text-sm text-muted-foreground">
                        No planned messages found for this contact.
                        {selectedContact.priority === 3 && selectedContact.stage === 'new'
                          ? ' This contact will receive batch messages when processed.'
                          : ' Follow-up messages will be determined by AI analysis when conversations are gathered.'
                        }
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button onClick={() => setShowMessagesDialog(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Change Priority Dialog */}
      <Dialog open={showPriorityDialog} onOpenChange={setShowPriorityDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change Priority for @{selectedContact?.username}</DialogTitle>
            <DialogDescription>
              Select a new priority level for this contact
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Select value={newPriority.toString()} onValueChange={(value) => setNewPriority(parseInt(value))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 - Highly Engaged</SelectItem>
                <SelectItem value="4">4 - Engaged</SelectItem>
                <SelectItem value="3">3 - New Followers</SelectItem>
                <SelectItem value="2">2 - Low Engaged</SelectItem>
                <SelectItem value="1">1 - Follow-up</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPriorityDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleChangePriority} disabled={isLoading}>
              {isLoading ? 'Updating...' : 'Update Priority'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Selected Users</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove {selectedItems.size} users from the attack list?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleBulkDelete} disabled={isLoading}>
              {isLoading ? 'Deleting...' : 'Delete Selected'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
