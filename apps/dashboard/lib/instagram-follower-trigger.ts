import { prisma } from '@workspace/database/client';
import { generateInstagramResponse } from '@workspace/instagram-bot';
import { getAllConversations, getConversationMessages } from '~/lib/instagram-client';


/**
 * Trigger processing for a new Instagram follower
 * This function processes the follower directly without API calls
 */
export async function triggerFollowerProcessing(followerId: string) {
  try {
    console.log(`🚀 Triggering processing for follower: ${followerId}`);

    // Get the follower details
    const follower = await prisma.instagramFollower.findUnique({
      where: { id: followerId }
    });

    if (!follower) {
      console.error(`❌ Follower not found: ${followerId}`);
      return { success: false, error: 'Follower not found' };
    }

    if (!follower.automationEnabled) {
      console.log(`⏸️ Automation disabled for follower: ${follower.instagramNickname}`);
      return { success: false, error: 'Automation disabled' };
    }

    if (follower.status !== 'pending') {
      console.log(`⏭️ Follower already processed: ${follower.instagramNickname} (${follower.status})`);
      return { success: false, error: 'Already processed' };
    }

    // Get Instagram settings for this organization
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId: follower.organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings?.instagramToken) {
      console.error(`❌ No Instagram token for organization: ${follower.organizationId}`);
      return { success: false, error: 'No Instagram token' };
    }

    // Check if InstagramContact already exists
    const existingContact = await prisma.instagramContact.findFirst({
      where: {
        organizationId: follower.organizationId,
        instagramNickname: follower.instagramNickname
      }
    });

    if (existingContact) {
      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: {
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      console.log(`✅ Follower already exists as contact: ${follower.instagramNickname}`);
      return {
        success: true,
        data: {
          action: 'marked_as_contacted',
          existingContactId: existingContact.id
        }
      };
    }

    // STEP 1: Check if InstagramConversationsNotGathered table is populated
    console.log(`Checking if conversation gathering has been done for organization: ${follower.organizationId}`);
    const existingConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId: follower.organizationId
      }
    });

    // If no conversations in table, this is FIRST TIME - run conversation gathering
    if (existingConversations === 0) {
      console.log(`🚀 FIRST FOLLOWER DETECTED - Running conversation gathering for organization: ${follower.organizationId}`);

      try {
        // Get all conversations from Instagram API
        console.log('Getting all conversations from Instagram API...');
        const instagramApiResponse = await getAllConversations(instagramSettings.instagramToken);
        const instagramApiConversations = instagramApiResponse.data || [];

        console.log(`Found ${instagramApiConversations.length} conversations to process`);

        // Populate InstagramConversationsNotGathered table
        for (const conversation of instagramApiConversations) {
          const participants = conversation.participants || [];
          if (participants.length >= 2) {
            // Skip the first participant (business account) and take the second one
            const participant = participants[1];
            if (participant.username) {
              try {
                await prisma.instagramConversationsNotGathered.create({
                  data: {
                    organizationId: follower.organizationId,
                    instagramConversationId: conversation.id,
                    participantUsername: participant.username,
                    participantId: participant.id,
                    updatedTime: new Date(conversation.updated_time),
                    isGathered: false
                  }
                });
                console.log(`✅ Added conversation for: ${participant.username}`);
              } catch (error) {
                // Ignore duplicates
                if (!(error instanceof Error) || !error.message?.includes('unique constraint')) {
                  console.error(`Error adding conversation for ${participant.username}:`, error);
                }
              }
            }
          }
        }

        console.log(`✅ Conversation gathering completed. Populated ${instagramApiConversations.length} conversations.`);
      } catch (error) {
        console.error(`❌ Error during conversation gathering:`, error);
        // Continue with processing even if gathering fails
      }
    } else {
      console.log(`✅ Conversation gathering already done. Found ${existingConversations} existing conversations.`);
    }

    // STEP 2: Now check if this specific follower has a conversation
    console.log(`Checking for conversation with follower: ${follower.instagramNickname}`);
    const conversationData = await prisma.instagramConversationsNotGathered.findFirst({
      where: {
        organizationId: follower.organizationId,
        participantUsername: follower.instagramNickname
      }
    });

    const hasConversation = !!conversationData;

    if (!hasConversation) {
      // No conversation - check if contact exists, if not create with priority 3 and batch messages
      console.log(`📝 No conversation found for ${follower.instagramNickname} - checking if contact exists`);

      // Check if contact already exists
      const existingContact = await prisma.instagramContact.findFirst({
        where: {
          organizationId: follower.organizationId,
          instagramNickname: follower.instagramNickname
        }
      });

      if (existingContact) {
        // Mark follower as contacted and ensure contact is on attack list
        await prisma.instagramFollower.update({
          where: { id: follower.id },
          data: {
            status: 'contacted',
            isContacted: true,
            updatedAt: new Date()
          }
        });

        // Ensure existing contact is on attack list with proper timing
        await prisma.instagramContact.update({
          where: { id: existingContact.id },
          data: {
            attackListStatus: 'pending',
            nextMessageAt: new Date(), // Ready to message immediately
            updatedAt: new Date()
            // Note: lastInteractionAt should only be set when actual message is sent
          }
        });

        console.log(`✅ Existing contact ${follower.instagramNickname} added to attack list`);
        return {
          success: true,
          data: {
            action: 'existing_contact_added_to_attack_list',
            contactId: existingContact.id,
            priority: existingContact.priority
          }
        };
      }

      // Get available message batches
      const messageBatches = await prisma.messageBatch.findMany({
        where: {
          organizationId: follower.organizationId,
          isActive: true
        },
        include: {
          MessageBatchItem: {
            orderBy: { sequenceNumber: 'asc' }
          }
        }
      });

      const batchesWithMessages = messageBatches.filter(
        batch => batch.MessageBatchItem.length > 0
      );

      const randomBatch = batchesWithMessages.length > 0
        ? batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)]
        : null;

      const newContact = await prisma.instagramContact.create({
        data: {
          organizationId: follower.organizationId,
          userId: follower.userId,
          instagramId: follower.instagramId,
          instagramNickname: follower.instagramNickname,
          avatar: follower.avatar,
          followerCount: follower.followerCount,
          isVerifiedUser: follower.isVerified,
          stage: 'new',
          priority: 3, // New followers get priority 3
          status: 'pending',
          messageCount: 0,
          isIgnored: false,
          isTakeControl: false,
          isConversionLinkSent: false,
          nextMessageAt: new Date(), // Ready to message immediately
          attackListStatus: 'pending',
          conversationSource: 'extension',
          batchId: randomBatch?.id
        }
      });

      // 🚀 CRITICAL: Keep nextMessageAt as NOW for initial message
      // The contact was created with nextMessageAt: new Date() which means the first message is available immediately
      // Batch messages (msg1, msg2, msg3) will be sent in sequence by Chrome extension
      // Follow-ups from FollowUpTemplate will be created AFTER the entire batch is sent
      console.log(`✅ Contact ${follower.instagramNickname} ready for immediate messaging - batch messages will be sent in sequence, follow-ups created after batch completion`);

      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: {
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      console.log(`✅ Follower processed with batch messages: ${follower.instagramNickname}`);
      return {
        success: true,
        data: {
          action: 'created_with_batch_messages',
          contactId: newContact.id,
          priority: 3,
          batchId: randomBatch?.id
        }
      };

    } else {
      // Has conversation - get full conversation and analyze with AI
      console.log(`💬 Conversation found for ${follower.instagramNickname} - analyzing with AI`);

      const conversationResponse = await getConversationMessages(conversationData.instagramConversationId, instagramSettings.instagramToken);

      // Format conversation history for AI
      let conversationHistory = '';
      if (conversationResponse && conversationResponse.data && conversationResponse.data.length > 0) {
        const conversation = conversationResponse.data[0];
        const messages = conversation.messages?.data || [];

        if (messages.length > 0) {
          const sortedMessages = messages.sort((a: any, b: any) =>
            new Date(a.created_time).getTime() - new Date(b.created_time).getTime()
          );

          conversationHistory = sortedMessages.map((msg: any) => {
            const sender = msg.from?.username || msg.from?.id || 'Unknown';
            const messageText = msg.message || '[Media/Attachment]';
            return `${sender}: ${messageText}`;
          }).join('\n');

          // Add last user interaction timestamp
          const lastUserMessage = sortedMessages
            .filter((msg: any) => msg.from?.username === follower.instagramNickname)
            .pop();

          if (lastUserMessage) {
            const lastInteractionTime = new Date(lastUserMessage.created_time);
            conversationHistory += `\n\nLAST USER INTERACTION: ${lastInteractionTime.toISOString()}`;
          }
        } else {
          conversationHistory = 'No messages found in conversation';
        }
      } else {
        conversationHistory = 'No messages found in conversation';
      }

      // Pass to AI for analysis with CONVERSATION GATHERING mode
      console.log(`🤖 Analyzing conversation with AI for ${follower.instagramNickname}...`);
      console.log(`📝 Conversation history being sent to AI:`, conversationHistory.substring(0, 500) + '...');

      // Use the conversation gathering mode - the generateInstagramResponse function
      // will automatically detect this and build the full prompt using buildConversationGatheringPrompt
      const aiResponse = await generateInstagramResponse({
        prompt: "CONVERSATION GATHERING", // This triggers the conversation gathering prompt builder
        conversationHistory: conversationHistory,
        organizationId: follower.organizationId // Use original org ID so prompt builder works correctly
      });

      console.log(`AI analysis for ${follower.instagramNickname}:`, {
        stage: aiResponse.stage,
        priority: aiResponse.priority,
        followUpsCount: aiResponse.followUps?.length || 0
      });

      // Check if contact already exists
      const existingContact = await prisma.instagramContact.findFirst({
        where: {
          organizationId: follower.organizationId,
          instagramNickname: follower.instagramNickname
        }
      });

      if (existingContact) {
        console.log(`📝 Contact already exists: ${follower.instagramNickname} - Updating with new AI follow-ups`);

        // Clear existing follow-ups for this contact
        await prisma.instagramFollowUp.deleteMany({
          where: { contactId: existingContact.id }
        });

        // Add new AI-generated follow-ups
        let earliestFollowUpTime = null;
        if (aiResponse.followUps && aiResponse.followUps.length > 0) {
          console.log(`📝 Creating ${aiResponse.followUps.length} new follow-ups for existing contact ${follower.instagramNickname}`);
          for (let i = 0; i < aiResponse.followUps.length; i++) {
            const followUp = aiResponse.followUps[i];
            const scheduledTime = new Date(followUp.delayHours ? Date.now() + (followUp.delayHours * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000));

            // Track earliest follow-up time for attack list
            if (!earliestFollowUpTime || scheduledTime < earliestFollowUpTime) {
              earliestFollowUpTime = scheduledTime;
            }

            await prisma.instagramFollowUp.create({
              data: {
                contactId: existingContact.id,
                message: followUp.message,
                scheduledTime: scheduledTime,
                status: 'external', // External so it appears in attack list
                sequenceNumber: i + 1
              }
            });
            console.log(`✅ Created follow-up ${i + 1} for existing contact: ${existingContact.id}`);
          }
        }

        // Update existing contact with new AI data and attack list status
        await prisma.instagramContact.update({
          where: { id: existingContact.id },
          data: {
            stage: (aiResponse.stage as any) || existingContact.stage,
            priority: aiResponse.priority || existingContact.priority,
            nextMessageAt: earliestFollowUpTime || new Date(),
            attackListStatus: 'pending', // Put on attack list
            messageCount: conversationResponse?.data?.[0]?.messages?.data?.length || existingContact.messageCount,
            updatedAt: new Date()
            // Note: lastInteractionAt should only be set when actual message is sent
          }
        });

        // Save conversation messages to database if not already saved
        const messages = conversationResponse?.data?.[0]?.messages?.data || [];
        if (messages.length > 0) {
          console.log(`Saving ${messages.length} messages to database for existing contact ${follower.instagramNickname}...`);

          for (const msg of messages) {
            try {
              // Check if message already exists
              const existingMessage = await prisma.instagramMessage.findFirst({
                where: {
                  contactId: existingContact.id,
                  messageId: msg.id
                }
              });

              if (!existingMessage) {
                await prisma.instagramMessage.create({
                  data: {
                    contactId: existingContact.id,
                    messageId: msg.id,
                    content: msg.message || '[Media/Attachment]',
                    isFromUser: msg.from?.username === follower.instagramNickname,
                    timestamp: new Date(msg.created_time),
                    mediaType: msg.attachments?.[0]?.mime_type || null,
                    mediaUrl: msg.attachments?.[0]?.file_url || null
                  }
                });
              }
            } catch (error) {
              console.error(`Error saving message ${msg.id}:`, error);
            }
          }
          console.log(`✅ Saved messages for existing contact ${follower.instagramNickname}`);
        }

        // Mark follower as contacted
        await prisma.instagramFollower.update({
          where: { id: follower.id },
          data: {
            status: 'contacted',
            isContacted: true,
            updatedAt: new Date()
          }
        });

        // Mark conversation as gathered
        await prisma.instagramConversationsNotGathered.updateMany({
          where: {
            organizationId: follower.organizationId,
            instagramConversationId: conversationData.instagramConversationId
          },
          data: {
            isGathered: true
          }
        });

        console.log(`✅ Updated existing contact ${follower.instagramNickname} with AI priority ${aiResponse.priority} and ${aiResponse.followUps?.length || 0} follow-ups - Added to attack list`);
        return {
          success: true,
          data: {
            action: 'updated_existing_contact',
            contactId: existingContact.id,
            priority: aiResponse.priority || existingContact.priority,
            stage: aiResponse.stage || existingContact.stage,
            followUpsCreated: aiResponse.followUps?.length || 0
          }
        };
      }

      // Create new contact with AI-determined priority and stage (using proven logic)
      const newContact = await prisma.instagramContact.create({
        data: {
          organizationId: follower.organizationId,
          userId: follower.userId,
          instagramId: follower.instagramId,
          instagramNickname: follower.instagramNickname,
          avatar: follower.avatar,
          followerCount: follower.followerCount,
          isVerifiedUser: follower.isVerified,
          stage: (aiResponse.stage as any) || 'initial',
          priority: aiResponse.priority || 3,
          status: 'pending',
          messageCount: conversationResponse?.data?.[0]?.messages?.data?.length || 0,
          isIgnored: false,
          isTakeControl: false,
          isConversionLinkSent: false,
          nextMessageAt: new Date(), // Ready for follow-up
          attackListStatus: 'pending',
          conversationSource: 'extension'
        }
      });

      // Save conversation messages to database
      const messages = conversationResponse?.data?.[0]?.messages?.data || [];
      if (messages.length > 0) {
        for (const msg of messages) {
          try {
            await prisma.instagramMessage.create({
              data: {
                contactId: newContact.id,
                messageId: msg.id,
                content: msg.message || '[Media/Attachment]',
                isFromUser: msg.from?.username === follower.instagramNickname,
                timestamp: new Date(msg.created_time),
                mediaType: msg.attachments?.[0]?.mime_type || null,
                mediaUrl: msg.attachments?.[0]?.file_url || null
              }
            });
          } catch (error) {
            console.error(`Error saving message ${msg.id}:`, error);
          }
        }
      }

      // Add AI-generated follow-ups if provided
      console.log(`🔍 AI Response for ${follower.instagramNickname}:`, {
        hasFollowUps: !!aiResponse.followUps,
        followUpsLength: aiResponse.followUps?.length || 0,
        priority: aiResponse.priority,
        stage: aiResponse.stage,
        fu1_message: aiResponse.fu1_message,
        fu2_message: aiResponse.fu2_message,
        rawResponse: JSON.stringify(aiResponse).substring(0, 200) + '...'
      });

      // Add follow-ups if AI provided them and update attack list timing
      let earliestFollowUpTime = null;
      if (aiResponse.followUps && aiResponse.followUps.length > 0) {
        console.log(`📝 Creating ${aiResponse.followUps.length} follow-ups for ${follower.instagramNickname}`);
        for (let i = 0; i < aiResponse.followUps.length; i++) {
          const followUp = aiResponse.followUps[i];
          const scheduledTime = new Date(followUp.delayHours ? Date.now() + (followUp.delayHours * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000));

          // Track earliest follow-up time for attack list
          if (!earliestFollowUpTime || scheduledTime < earliestFollowUpTime) {
            earliestFollowUpTime = scheduledTime;
          }

          const followUpRecord = await prisma.instagramFollowUp.create({
            data: {
              contactId: newContact.id,
              message: followUp.message,
              scheduledTime: scheduledTime,
              status: 'external', // External so it appears in attack list
              sequenceNumber: i + 1
            }
          });
          console.log(`✅ Created follow-up ${i + 1}: ${followUpRecord.id}`);
        }

        // 🚀 CRITICAL: Update contact's nextMessageAt to earliest follow-up time for attack list
        await prisma.instagramContact.update({
          where: { id: newContact.id },
          data: {
            nextMessageAt: earliestFollowUpTime,
            attackListStatus: 'pending' // Ensure it's in attack list
            // Note: lastInteractionAt should only be set when actual message is sent
          }
        });

        console.log(`✅ Updated ${follower.instagramNickname} nextMessageAt to ${earliestFollowUpTime?.toISOString()} for attack list`);
      } else {
        console.log(`⚠️ No follow-ups generated by AI for ${follower.instagramNickname}`);
      }

      // Update Instagram follower to mark as contacted (using proven logic)
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: {
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      // Mark conversation as gathered in InstagramConversationsNotGathered (using proven logic)
      await prisma.instagramConversationsNotGathered.updateMany({
        where: {
          organizationId: follower.organizationId,
          instagramConversationId: conversationData.instagramConversationId
        },
        data: {
          isGathered: true
        }
      });

      console.log(`✅ Added ${follower.instagramNickname} to attack list with AI priority ${aiResponse.priority} and ${aiResponse.followUps?.length || 0} follow-ups`);
      return {
        success: true,
        data: {
          action: 'created_with_ai_analysis',
          contactId: newContact.id,
          priority: aiResponse.priority || 3,
          stage: aiResponse.stage || 'initial',
          followUpsCreated: aiResponse.followUps?.length || 0
        }
      };
    }

  } catch (error) {
    console.error(`💥 Error triggering follower processing: ${followerId}`, error);
    return { success: false, error: 'Internal error' };
  }
}

/**
 * Trigger processing for multiple followers by nickname
 */
export async function triggerFollowerProcessingByNicknames(nicknames: string[], organizationId: string) {
  const results = {
    processed: 0,
    errors: [] as string[],
    successes: [] as string[]
  };

  for (const nickname of nicknames) {
    try {
      // Find the follower
      const follower = await prisma.instagramFollower.findFirst({
        where: {
          organizationId,
          instagramNickname: nickname,
          status: 'pending',
          automationEnabled: true
        }
      });

      if (!follower) {
        results.errors.push(`${nickname}: Not found or already processed`);
        continue;
      }

      // Trigger processing
      const result = await triggerFollowerProcessing(follower.id);

      if (result.success) {
        results.successes.push(nickname);
        results.processed++;
      } else {
        results.errors.push(`${nickname}: ${result.error}`);
      }

      // Add small delay to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay

    } catch (error) {
      results.errors.push(`${nickname}: ${error}`);
    }
  }

  return results;
}

/**
 * Auto-trigger processing for all pending followers in an organization
 */
export async function autoTriggerPendingFollowers(organizationId: string, limit: number = 10) {
  try {
    console.log(`🔄 Auto-triggering processing for organization: ${organizationId}`);

    // Get pending followers
    const pendingFollowers = await prisma.instagramFollower.findMany({
      where: {
        organizationId,
        status: 'pending',
        automationEnabled: true
      },
      take: limit,
      orderBy: {
        createdAt: 'asc' // Process oldest first
      }
    });

    if (pendingFollowers.length === 0) {
      console.log(`✅ No pending followers to process for organization: ${organizationId}`);
      return { success: true, processed: 0, message: 'No pending followers' };
    }

    console.log(`📋 Found ${pendingFollowers.length} pending followers to process`);

    const results = {
      processed: 0,
      errors: [] as string[],
      successes: [] as string[]
    };

    for (const follower of pendingFollowers) {
      try {
        const result = await triggerFollowerProcessing(follower.id);

        if (result.success) {
          results.successes.push(follower.instagramNickname);
          results.processed++;
        } else {
          results.errors.push(`${follower.instagramNickname}: ${result.error}`);
        }

        // Add delay between followers
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay

      } catch (error) {
        results.errors.push(`${follower.instagramNickname}: ${error}`);
      }
    }

    console.log(`✅ Auto-trigger completed for organization ${organizationId}:`, results);
    return { success: true, ...results };

  } catch (error) {
    console.error(`💥 Error in auto-trigger for organization ${organizationId}:`, error);
    return { success: false, error: 'Auto-trigger failed' };
  }
}

/**
 * Check if a follower needs processing
 */
export async function checkFollowerNeedsProcessing(instagramNickname: string, organizationId: string): Promise<boolean> {
  try {
    // Check if follower exists and is pending
    const follower = await prisma.instagramFollower.findFirst({
      where: {
        organizationId,
        instagramNickname,
        status: 'pending',
        automationEnabled: true
      }
    });

    if (!follower) {
      return false;
    }

    // Check if contact already exists
    const existingContact = await prisma.instagramContact.findFirst({
      where: {
        organizationId,
        instagramNickname
      }
    });

    // Needs processing if follower is pending and no contact exists
    return !existingContact;

  } catch (error) {
    console.error(`Error checking if follower needs processing: ${instagramNickname}`, error);
    return false;
  }
}

/**
 * Get processing statistics for an organization
 */
export async function getProcessingStats(organizationId: string) {
  try {
    const [
      totalFollowers,
      pendingFollowers,
      contactedFollowers,
      totalContacts,
      attackListCount
    ] = await Promise.all([
      prisma.instagramFollower.count({
        where: { organizationId }
      }),
      prisma.instagramFollower.count({
        where: {
          organizationId,
          status: 'pending',
          automationEnabled: true
        }
      }),
      prisma.instagramFollower.count({
        where: {
          organizationId,
          status: 'contacted'
        }
      }),
      prisma.instagramContact.count({
        where: { organizationId }
      }),
      prisma.instagramContact.count({
        where: {
          organizationId,
          attackListStatus: 'pending'
        }
      })
    ]);

    return {
      totalFollowers,
      pendingFollowers,
      contactedFollowers,
      totalContacts,
      attackListCount,
      processingRate: totalFollowers > 0 ? (contactedFollowers / totalFollowers) * 100 : 0,
      needsProcessing: pendingFollowers > 0
    };

  } catch (error) {
    console.error(`Error getting processing stats for organization ${organizationId}:`, error);
    return null;
  }
}
