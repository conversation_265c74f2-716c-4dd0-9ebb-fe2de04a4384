'use server';

import { revalidateTag } from 'next/cache';

import { NotFoundError } from '@workspace/common/errors';
import { prisma } from '@workspace/database/client';

import { updateContactAndCaptureEvent } from '~/actions/contacts/_contact-event-capture';
import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';
import { updateContactStageSchema } from '~/schemas/contacts/update-contact-stage-schema';
import { 
  handleContactDisqualification, 
  shouldTriggerDisqualificationCleanup 
} from '~/lib/contact-disqualification-cleanup';

export const updateContactStage = authOrganizationActionClient
  .metadata({ actionName: 'updateContactStage' })
  .schema(updateContactStageSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Check if contact exists and get current stage
    const contact = await prisma.contact.findFirst({
      where: {
        organizationId: ctx.organization.id,
        id: parsedInput.id
      },
      select: { id: true, stage: true, email: true, phone: true, name: true }
    });
    
    if (!contact) {
      throw new NotFoundError('Contact not found');
    }

    const currentStage = contact.stage;
    const newStage = parsedInput.stage;

    // Update the contact stage
    await updateContactAndCaptureEvent(
      parsedInput.id,
      { stage: newStage },
      ctx.session.user.id
    );

    // Handle Instagram contact disqualification if this is an Instagram contact
    // Note: This is for regular Contact model, but we should also check if there's
    // a corresponding InstagramContact that needs cleanup
    if (newStage === 'LOST') { // Contact marked as lost - handle Instagram cleanup
      try {
        // Find corresponding Instagram contact by looking for same email/phone/name
        const instagramContact = await prisma.instagramContact.findFirst({
          where: {
            organizationId: ctx.organization.id,
            OR: [
              // Try to match by email if available
              contact.email ? { email: contact.email } : {},
              // Could add more matching logic here based on available fields
            ]
          }
        });

        if (instagramContact && shouldTriggerDisqualificationCleanup(instagramContact.stage, 'disqualified')) {
          console.log(`Regular contact stage changed to lost, checking Instagram contact ${instagramContact.id} for cleanup...`);
          
          const cleanupResult = await handleContactDisqualification(
            instagramContact.id,
            ctx.organization.id,
            `Contact stage changed to lost via regular contact update`
          );

          if (cleanupResult.success) {
            console.log(`Instagram contact cleanup completed:`, cleanupResult.actions);
          }
        }
      } catch (error) {
        console.error('Error handling Instagram contact cleanup for regular contact update:', error);
        // Don't fail the main operation if Instagram cleanup fails
      }
    }

    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Contacts,
        ctx.organization.id
      )
    );
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Contact,
        ctx.organization.id,
        parsedInput.id
      )
    );
  });
