'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';

const saveAdminSettingsSchema = z.object({
  id: z.string().uuid().optional(),
  cacheForAllUsers: z.boolean(),
  cacheType: z.enum(['5m', '1h']).default('5m'),
  enableConversationCache: z.boolean().default(true),
  maxConversationMessages: z.number().min(50).max(500).default(200)
});

export const saveAdminSettings = authOrganizationActionClient
  .metadata({ actionName: 'saveAdminSettings' })
  .schema(saveAdminSettingsSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Only allow SaaS admin (<EMAIL>) to save admin settings
    if (ctx.session.user.email !== '<EMAIL>') {
      throw new Error('Unauthorized: Only SaaS admin can save admin settings');
    }

    // Check if settings already exist
    const existingSettings = await prisma.adminSettings.findFirst();

    if (existingSettings) {
      // Update existing settings
      await prisma.adminSettings.update({
        where: { id: existingSettings.id },
        data: {
          cacheForAllUsers: parsedInput.cacheForAllUsers,
          cacheType: parsedInput.cacheType,
          enableConversationCache: parsedInput.enableConversationCache,
          maxConversationMessages: parsedInput.maxConversationMessages
        }
      });
    } else {
      // Create new settings
      await prisma.adminSettings.create({
        data: {
          cacheForAllUsers: parsedInput.cacheForAllUsers,
          cacheType: parsedInput.cacheType,
          enableConversationCache: parsedInput.enableConversationCache,
          maxConversationMessages: parsedInput.maxConversationMessages
        }
      });
    }

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.AdminSettings,
        'global'
      )
    );

    return { success: true };
  });


