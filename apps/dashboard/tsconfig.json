{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "moduleResolution": "bundler", "target": "es2015", "incremental": true, "tsBuildInfoFile": ".next/cache/tsconfig.tsbuildinfo", "skipLibCheck": true, "paths": {"~/*": ["./*"], "@workspace/ui/*": ["../../packages/ui/src/*"], "@workspace/common/*": ["../../packages/common/src/*"]}}, "include": ["next-env.d.ts", "next-page.d.ts", "next-params.d.ts", "**/*.ts", "**/*.tsx", "*.mjs", ".next/types/**/*.ts"], "exclude": [".cache", ".next", ".turbo", "build", "dist", "node_modules"]}