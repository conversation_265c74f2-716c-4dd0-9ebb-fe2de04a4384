import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { verifyApi<PERSON>ey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

interface MessageToSend {
  id: string;
  username: string;
  message: string;
  type: 'batch' | 'batch_sequence' | 'followup';
  followUpId?: string;
  sequenceNumber?: number;
  batchMessages?: Array<{
    sequenceNumber: number;
    message: string;
    delayMinutes: number;
  }>;
}

/**
 * Get simplified messages ready to send for Chrome Extension
 * Returns only essential data: ID, Username, Message when time to send has arrived
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;
    const now = new Date();

    // Get contacts ready for messaging (time has arrived AND currently in attack list)
    const readyContacts = await prisma.instagramContact.findMany({
      where: {
        organizationId,
        attackListStatus: 'pending', // Must be in attack list
        conversationSource: 'extension',
        stage: {
          notIn: ['converted', 'disqualified', 'blocked', 'suspicious'] // Exclude terminal stages (same as attack-list endpoint)
        }
      },
      include: {
        InstagramFollowUp: {
          where: {
            status: 'external'
          },
          orderBy: { sequenceNumber: 'asc' }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { nextMessageAt: 'asc' }
      ]
    });

    // Process contacts and handle overdue follow-up rescheduling
    const contactsWithReadyMessages = [];
    
    for (const contact of readyContacts) {
      const followUps = contact.InstagramFollowUp;
      
      if (followUps.length > 0) {
        // Check for overdue follow-ups that need rescheduling
        const overdueFollowUps = followUps.filter(f => f.scheduledTime <= now);
        const futureFollowUps = followUps.filter(f => f.scheduledTime > now);
        
        console.log(`📊 Contact ${contact.instagramNickname}: ${followUps.length} total follow-ups, ${overdueFollowUps.length} overdue, ${futureFollowUps.length} future`);
        
        if (overdueFollowUps.length > 1) {
          console.log(`🔄 Contact ${contact.instagramNickname} has ${overdueFollowUps.length} overdue follow-ups - rescheduling to maintain intervals`);
          console.log(`📅 Original schedule:`, followUps.map(f => ({ seq: f.sequenceNumber, time: f.scheduledTime.toISOString() })));
          
          // Calculate original intervals between follow-ups
          const intervals: number[] = [];
          const minIntervalMs = 24 * 60 * 60 * 1000; // Minimum 24 hours between follow-ups
          
          for (let i = 1; i < followUps.length; i++) {
            const prevFollowUp = followUps[i - 1];
            const currentFollowUp = followUps[i];
            const originalInterval = currentFollowUp.scheduledTime.getTime() - prevFollowUp.scheduledTime.getTime();
            
            // Use original interval if reasonable, otherwise use minimum 24h
            const interval = originalInterval > minIntervalMs ? originalInterval : minIntervalMs;
            intervals.push(interval);
          }
          
          console.log(`📊 Calculated intervals for ${contact.instagramNickname}:`, intervals.map(i => `${Math.round(i / (1000 * 60 * 60))}h`));
          
          // Reschedule follow-ups starting from now, maintaining calculated intervals
          const reschedulePromises = [];
          let nextScheduleTime = now.getTime();
          
          for (let i = 0; i < followUps.length; i++) {
            const followUp = followUps[i];
            const newScheduledTime = new Date(nextScheduleTime);
            
            // Always update if time is different (includes first follow-up set to "now")
            if (Math.abs(followUp.scheduledTime.getTime() - nextScheduleTime) > 1000) { // 1 second tolerance
              reschedulePromises.push(
                prisma.instagramFollowUp.update({
                  where: { id: followUp.id },
                  data: { scheduledTime: newScheduledTime }
                })
              );
              console.log(`📅 Rescheduling ${contact.instagramNickname} follow-up #${followUp.sequenceNumber} from ${followUp.scheduledTime.toISOString()} to ${newScheduledTime.toISOString()}`);
            }
            
            // Calculate next schedule time using calculated intervals
            if (i < intervals.length) {
              nextScheduleTime += intervals[i];
            } else {
              nextScheduleTime += 24 * 60 * 60 * 1000; // Default 24h interval for any extra follow-ups
            }
          }
          
          // Execute all reschedule operations
          if (reschedulePromises.length > 0) {
            await Promise.all(reschedulePromises);
            
            // Refresh follow-up data after rescheduling
            const updatedContact = await prisma.instagramContact.findUnique({
              where: { id: contact.id },
              include: {
                InstagramFollowUp: {
                  where: { status: 'external' },
                  orderBy: { sequenceNumber: 'asc' }
                }
              }
            });
            
            if (updatedContact) {
              // Update contact's follow-ups with fresh data
              contact.InstagramFollowUp = updatedContact.InstagramFollowUp;
            }
          }
        }
        
        // Check if this contact has any follow-up ready now (using current/updated data)
        const nextReadyFollowUp = contact.InstagramFollowUp.find(f => f.scheduledTime <= now);
        
        if (nextReadyFollowUp) {
          contactsWithReadyMessages.push(contact);
        }
      } else if (contact.priority === 3 && contact.stage === 'new' && (!contact.nextMessageAt || contact.nextMessageAt <= now)) {
        // New follower ready for initial batch message
        contactsWithReadyMessages.push(contact);
      }
    }

    // Get Chrome Extension settings to check smartFocus
    const chromeExtensionSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    const smartFocus = chromeExtensionSettings?.smartFocus ?? false;

    // Apply smart focus sorting if enabled (priority 3 first)
    let sortedContacts = contactsWithReadyMessages;
    if (smartFocus) {
      sortedContacts = [
        ...contactsWithReadyMessages.filter(c => c.priority === 3),
        ...contactsWithReadyMessages.filter(c => c.priority !== 3)
      ];
    }

    // Get message batches for new followers without conversations
    const messageBatches = await prisma.messageBatch.findMany({
      where: { organizationId },
      include: {
        MessageBatchItem: {
          orderBy: { sequenceNumber: 'asc' }
        }
      }
    });

    const messagesToSend: MessageToSend[] = [];

    for (const contact of sortedContacts) {
      // Find the next follow-up that should be sent (earliest overdue only)
      const nextFollowUp = contact.InstagramFollowUp.find(f => f.scheduledTime <= now);
      
      if (nextFollowUp) {
        // Contact has AI follow-up ready to send
        messagesToSend.push({
          id: contact.id,
          username: contact.instagramNickname,
          message: nextFollowUp.message,
          type: 'followup',
          followUpId: nextFollowUp.id,
          sequenceNumber: nextFollowUp.sequenceNumber
        });
      } else if (contact.priority === 3 && contact.stage === 'new') {
        // Contact is new follower without conversations - return ENTIRE batch sequence
        const batchesWithMessages = messageBatches.filter(
          batch => batch.MessageBatchItem.length > 0
        );

        if (batchesWithMessages.length > 0) {
          const randomIndex = Math.floor(Math.random() * batchesWithMessages.length);
          const selectedBatch = batchesWithMessages[randomIndex];

          // Return ALL messages in the batch - Chrome extension will handle delays
          messagesToSend.push({
            id: contact.id,
            username: contact.instagramNickname,
            message: selectedBatch.MessageBatchItem.map(item => item.messageText).join(' | '), // All messages separated
            type: 'batch_sequence',
            sequenceNumber: 1,
            batchMessages: selectedBatch.MessageBatchItem.map(item => ({
              sequenceNumber: item.sequenceNumber,
              message: item.messageText,
              delayMinutes: 0 // Chrome extension uses hardcoded 20-40 second delays between messages
            }))
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: messagesToSend,
      count: messagesToSend.length,
      metadata: {
        totalReadyContacts: contactsWithReadyMessages.length,
        totalInAttackList: readyContacts.length,
        smartFocusEnabled: smartFocus,
        timestamp: now.toISOString()
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error fetching messages to send:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
