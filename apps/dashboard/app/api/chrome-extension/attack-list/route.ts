import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { verifyApi<PERSON>ey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const UpdateAttackListItemSchema = z.object({
  status: z.enum(['pending', 'ready', 'in_progress', 'completed', 'failed']).optional(),
  priority: z.number().min(1).max(5).optional(),
  error: z.string().optional()
}).refine(data => data.status || data.priority, {
  message: "Either status or priority must be provided"
});

export interface AttackListItem {
  id: string;
  instagramId: string | null;
  username: string;
  avatar: string | null;
  priority: number;
  status: string;
  attackListStatus: string | null;
  nextMessageAt: Date | null;
  lastInteractionAt: Date | null;
  stage: string;
  conversationSource: string;
  createdAt: Date;
  updatedAt: Date;
  // New Chrome extension workflow fields
  batchMessageStatus: string | null;
  currentMessageSequence: number | null;
  lastMessageSentAt: Date | null;
  batchId: string | null;
  // Message tracking fields
  messageStatus?: {
    sent: number;
    total: number;
    currentSequence: number;
  };
  suggestedMessage?: {
    batchId: string;
    batchName: string;
    text: string;
  };
  plannedMessages?: {
    type: 'ai_followups' | 'batch_messages';
    messages: Array<{
      id?: string;
      sequenceNumber: number;
      message: string;
      scheduledTime?: Date;
      status?: string;
      delayMinutes?: number;
    }>;
  };
}

/**
 * Get Attack List for Chrome Extension
 * Returns InstagramContacts ready for messaging, sorted by priority and timing
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }
      organizationId = membership.organizationId;
    }

    const { searchParams } = new URL(req.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const priority = searchParams.get('priority');
    const status = searchParams.get('status');

    const where: any = {
      organizationId: organizationId,
      conversationSource: { in: ['extension', 'api'] }, // Include both extension and conversation gathering contacts
      stage: {
        notIn: ['converted', 'disqualified', 'blocked', 'suspicious'] // Exclude terminal stages
      }
      // Remove OR condition - include all extension/api contacts regardless of message count
      // Conversation gathering contacts have conversationSource: 'extension' and messageCount > 0
    };

    // Add filters
    if (priority) where.priority = parseInt(priority);
    if (status) where.attackListStatus = status;

    // Get current time for timing logic
    const now = new Date();

    // Get all contacts first, then filter and sort properly
    const allContacts = await prisma.instagramContact.findMany({
      where,
      select: {
        id: true,
        instagramId: true,
        instagramNickname: true,
        avatar: true,
        priority: true,
        status: true,
        attackListStatus: true,
        nextMessageAt: true,
        lastInteractionAt: true,
        stage: true,
        conversationSource: true,
        createdAt: true,
        updatedAt: true,
        // New fields for Chrome extension workflow
        batchMessageStatus: true,
        currentMessageSequence: true,
        lastMessageSentAt: true,
        batchId: true
      }
    });

    // Get contacts with AI follow-ups to identify conversation gathering contacts
    const contactsWithAIFollowUps = await prisma.instagramFollowUp.findMany({
      where: {
        contactId: { in: allContacts.map(c => c.id) },
        status: 'external' // AI-generated follow-ups from conversation gathering
      },
      select: {
        contactId: true
      }
    });

    const conversationGatheringContactIds = new Set(contactsWithAIFollowUps.map(f => f.contactId));

    // Filter contacts where time has come (nextMessageAt is null or <= now)
    // AND either have pending attack list status OR have to_send batch message status
    // EXCLUDE disqualified contacts from attack list
    // SPECIAL CASE: Conversation gathering contacts (with AI follow-ups) should always appear in attack list regardless of timing
    const readyContacts = allContacts.filter(contact => {
      // Exclude disqualified contacts
      if (contact.stage === 'disqualified') {
        return false;
      }

      const statusReady = contact.attackListStatus === 'pending' || contact.batchMessageStatus === 'to_send';

      // Conversation gathering contacts (with AI follow-ups) bypass time restrictions
      const isConversationGathering = conversationGatheringContactIds.has(contact.id);

      const timeReady = isConversationGathering || !contact.nextMessageAt || contact.nextMessageAt <= now;

      return timeReady && statusReady;
    });

    // Get Chrome Extension settings to check smartFocus
    const chromeExtensionSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId: organizationId }
    });

    const smartFocus = chromeExtensionSettings?.smartFocus ?? false;

    // Sort ready contacts based on smartFocus setting
    readyContacts.sort((a, b) => {
      // First: Sort by nextMessageAt (null values first, then earliest times)
      if (a.nextMessageAt && b.nextMessageAt) {
        const timeDiff = a.nextMessageAt.getTime() - b.nextMessageAt.getTime();
        if (timeDiff !== 0) return timeDiff;
      } else if (a.nextMessageAt && !b.nextMessageAt) {
        return 1; // b (null) comes first
      } else if (!a.nextMessageAt && b.nextMessageAt) {
        return -1; // a (null) comes first
      }

      // Then: Apply smartFocus logic for priority sorting
      if (smartFocus) {
        // SmartFocus: Priority 3 (new followers) first, then 5→4→2→1
        const getPriorityOrder = (priority: number) => {
          if (priority === 3) return 0; // Highest priority
          if (priority === 5) return 1;
          if (priority === 4) return 2;
          if (priority === 2) return 3;
          if (priority === 1) return 4;
          return 5; // Fallback
        };

        const aOrder = getPriorityOrder(a.priority);
        const bOrder = getPriorityOrder(b.priority);

        if (aOrder !== bOrder) {
          return aOrder - bOrder;
        }
      } else {
        // Normal priority: 5→4→3→2→1 (highest first)
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
      }

      // Finally: Sort by creation time (oldest first for same priority)
      return a.createdAt.getTime() - b.createdAt.getTime();
    });

    // Take only the requested limit
    const contacts = readyContacts.slice(0, limit);

    // Get message batches for priority 3 contacts
    let messageBatches: any[] = [];
    const hasPriority3Contacts = contacts.some(c => c.priority === 3 && c.stage === 'new');

    if (hasPriority3Contacts) {
      messageBatches = await prisma.messageBatch.findMany({
        where: {
          organizationId: organizationId
        },
        include: {
          MessageBatchItem: {
            orderBy: {
              sequenceNumber: 'asc' // Get all messages in order
            }
          }
        }
      });
    }

    // Get planned messages for all contacts
    const contactIds = contacts.map(c => c.id);

    // Fetch AI follow-ups for contacts that have them (include both pending and external)
    const aiFollowUps = await prisma.instagramFollowUp.findMany({
      where: {
        contactId: { in: contactIds },
        status: { in: ['pending', 'external'] } // Include conversation gathering follow-ups
      },
      orderBy: [
        { contactId: 'asc' },
        { sequenceNumber: 'asc' }
      ]
    });

    // Group follow-ups by contact ID
    const followUpsByContact = aiFollowUps.reduce((acc, followUp) => {
      if (!acc[followUp.contactId]) {
        acc[followUp.contactId] = [];
      }
      acc[followUp.contactId].push(followUp);
      return acc;
    }, {} as Record<string, typeof aiFollowUps>);

    const attackList: AttackListItem[] = await Promise.all(contacts.map(async contact => {
      const item: AttackListItem = {
        id: contact.id,
        instagramId: contact.instagramId,
        username: contact.instagramNickname,
        avatar: contact.avatar,
        priority: contact.priority,
        status: contact.status,
        attackListStatus: contact.attackListStatus,
        nextMessageAt: contact.nextMessageAt,
        lastInteractionAt: contact.lastInteractionAt,
        stage: contact.stage,
        conversationSource: contact.conversationSource,
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
        // New Chrome extension workflow fields
        batchMessageStatus: contact.batchMessageStatus,
        currentMessageSequence: contact.currentMessageSequence,
        lastMessageSentAt: contact.lastMessageSentAt,
        batchId: contact.batchId
      };

      // Calculate message status tracking
      const contactFollowUps = followUpsByContact[contact.id] || [];
      const sentFollowUps = contactFollowUps.filter(f => f.status === 'sent').length;
      const totalFollowUps = contactFollowUps.length;

      // For batch messages, check if initial message was sent
      let sentMessages = 0;
      let totalMessages = 0;
      const currentSequence = contact.currentMessageSequence || 1;

      if (contact.priority === 3 && contact.stage === 'new') {
        // This is a batch message contact
        const selectedBatch = messageBatches.find(batch =>
          batch.id === contact.batchId ||
          (batch.isDefault && !contact.batchId)
        );

        if (selectedBatch) {
          totalMessages = selectedBatch.MessageBatchItem.length;
          // If batchMessageStatus is 'sent', at least the first message was sent
          if (contact.batchMessageStatus === 'sent') {
            sentMessages = 1;
          }
          // Add any sent follow-ups
          sentMessages += sentFollowUps;
        }
      } else {
        // This is an AI follow-up contact
        totalMessages = totalFollowUps;
        sentMessages = sentFollowUps;
      }

      if (totalMessages > 0) {
        item.messageStatus = {
          sent: sentMessages,
          total: totalMessages,
          currentSequence: currentSequence
        };
      }

      // Determine planned messages based on contact type
      // PRIORITY: Show follow-ups first if they exist, then batch messages

      if (contactFollowUps && contactFollowUps.length > 0) {
        // Contact has AI follow-ups (from conversation gathering or template follow-ups) - show follow-ups
        item.plannedMessages = {
          type: 'ai_followups',
          messages: contactFollowUps.map(followUp => ({
            id: followUp.id,
            sequenceNumber: followUp.sequenceNumber,
            message: followUp.message,
            scheduledTime: followUp.scheduledTime,
            status: followUp.status
          }))
        };
      } else {
        // No follow-ups exist - show batch messages

        // Check if this contact has a specific batch assigned
        const contactBatch = contact.batchId ? messageBatches.find(batch => batch.id === contact.batchId) : null;

        if (contactBatch && contactBatch.MessageBatchItem.length > 0) {
          // Contact has a specific batch assigned - show the complete batch sequence
          const firstMessage = contactBatch.MessageBatchItem[0];
          item.suggestedMessage = {
            batchId: contactBatch.id,
            batchName: contactBatch.name,
            text: firstMessage.messageText
          };

          // Add planned batch messages (complete sequence including first message)
          item.plannedMessages = {
            type: 'batch_messages',
            messages: contactBatch.MessageBatchItem.map((batchItem: any) => ({
              sequenceNumber: batchItem.sequenceNumber,
              message: batchItem.messageText,
              delayMinutes: batchItem.delayMinutes
            }))
          };
        } else if (contact.priority === 3 && contact.stage === 'new') {
          // Contact is new follower without conversations, no specific batch, and no follow-ups yet - show random batch messages
          const batchesWithMessages = messageBatches.filter(
            batch => batch.MessageBatchItem.length > 0
          );

          if (batchesWithMessages.length > 0) {
            const randomIndex = Math.floor(Math.random() * batchesWithMessages.length);
            const selectedBatch = batchesWithMessages[randomIndex];

            // Add suggested message for backward compatibility
            const firstMessage = selectedBatch.MessageBatchItem[0];
            item.suggestedMessage = {
              batchId: selectedBatch.id,
              batchName: selectedBatch.name,
              text: firstMessage.messageText
            };

            // Add planned batch messages (immediate sequence)
            item.plannedMessages = {
              type: 'batch_messages',
              messages: selectedBatch.MessageBatchItem.map((batchItem: any) => ({
                sequenceNumber: batchItem.sequenceNumber,
                message: batchItem.messageText,
                delayMinutes: batchItem.delayMinutes
              }))
            };
          }
        }
      }

      return item;
    }));

    return NextResponse.json({
      success: true,
      data: attackList,
      total: contacts.length,
      metadata: {
        totalInDatabase: allContacts.length,
        readyForMessaging: readyContacts.length,
        description: 'Showing extension contacts (0 messages) and conversation gathering contacts (with AI follow-ups)',
        priorityBreakdown: {
          priority5: readyContacts.filter(c => c.priority === 5).length,
          priority4: readyContacts.filter(c => c.priority === 4).length,
          priority3: readyContacts.filter(c => c.priority === 3).length,
          priority2: readyContacts.filter(c => c.priority === 2).length,
          priority1: readyContacts.filter(c => c.priority === 1).length,
        }
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error fetching attack list:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Update Attack List Item Status
 */
export async function PUT(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }
      organizationId = membership.organizationId;
    }

    const { searchParams } = new URL(req.url);
    const contactId = searchParams.get('id');

    if (!contactId) {
      return NextResponse.json(
        { success: false, error: 'Contact ID is required' },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    const body = await req.json();
    const validatedData = UpdateAttackListItemSchema.parse(body);

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date()
    };

    if (validatedData.status) {
      updateData.attackListStatus = validatedData.status;
    }

    if (validatedData.priority) {
      updateData.priority = validatedData.priority;
    }

    // Update the contact
    const updatedContact = await prisma.instagramContact.update({
      where: {
        id: contactId,
        organizationId: organizationId
      },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: updatedContact
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error updating attack list item:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
