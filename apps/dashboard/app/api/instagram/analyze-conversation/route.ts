import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';
import { generateInstagramResponse } from '@workspace/instagram-bot';
import { logger, createTimer } from '@workspace/common/logger';
import { getUserIdFromUsername } from '~/lib/instagram-client';
import { 
  handleContactDisqualification, 
  shouldTriggerDisqualificationCleanup 
} from '~/lib/contact-disqualification-cleanup';

interface ConversationAnalysisRequest {
  organizationId: string;
  contactId?: string;
  username?: string;
  messages?: Array<{
    content: string;
    isFromUser: boolean;
    timestamp: string;
  }>;
}

interface ConversationAnalysisResponse {
  priority: number; // 1-5 scale (1=low, 5=high)
  status: 'new' | 'initial' | 'engaged' | 'qualified' | 'formsent' | 'converted' | 'disqualified';
  reason: string;
  lastMessageDate: string;
  engagementLevel: 'low' | 'medium' | 'high';
  responseTime: number; // hours since last message
  recommendations: string[];
}

/**
 * Analyze Instagram conversation using AI to determine priority and status
 */
export async function POST(req: NextRequest): Promise<Response> {
  const timer = createTimer('Instagram Conversation Analysis API');

  try {
    const body: ConversationAnalysisRequest = await req.json();
    const { organizationId, contactId, username, messages } = body;

    logger.instagramConversation('Starting conversation analysis', {
      operation: 'POST /api/instagram/analyze-conversation',
      organizationId,
      contactId: contactId || 'none',
      username: username || 'none',
      messageCount: messages?.length || 0
    });

    if (!organizationId) {
      return NextResponse.json({ error: 'Missing organizationId parameter' }, { status: 400 });
    }

    let conversationMessages = messages;
    let contact = null;
    let instagramId = null;

    // If contactId or username is provided, fetch messages from database
    if (contactId || username) {
      const whereClause = contactId
        ? { id: contactId, organizationId }
        : { organizationId, instagramNickname: username };

      contact = await prisma.instagramContact.findFirst({
        where: whereClause,
        include: {
          InstagramMessage: {
            orderBy: { timestamp: 'asc' },
            take: 50 // Limit to last 50 messages for analysis
          }
        }
      });

      // If contact not found and we have a username, try to convert username to ID and create contact
      if (!contact && username) {
        logger.instagramConversation('Contact not found, attempting username to ID conversion', {
          operation: 'username-to-id-conversion',
          organizationId,
          username
        });

        try {
          instagramId = await getUserIdFromUsername(username);

          if (instagramId) {
            logger.instagramConversation('Successfully converted username to Instagram ID', {
              operation: 'username-to-id-conversion',
              organizationId,
              username,
              instagramId
            });

            // Create a new contact with the converted Instagram ID
            contact = await prisma.instagramContact.create({
              data: {
                organizationId,
                userId: '8f5d5581-3256-46e2-a39e-22bb052f2c03', // Use the test user ID
                instagramNickname: username,
                instagramId: instagramId,
                stage: 'new',
                avatar: `https://i.pravatar.cc/150?u=${username}`,
                lastInteractionAt: new Date(),
                priority: 3 // Default priority for new contacts
              },
              include: {
                InstagramMessage: {
                  orderBy: { timestamp: 'asc' },
                  take: 50
                }
              }
            });

            logger.instagramConversation('Created new contact with converted Instagram ID', {
              operation: 'create-contact',
              organizationId,
              contactId: contact.id,
              username,
              instagramId
            });
          } else {
            logger.instagramConversation('Failed to convert username to Instagram ID', {
              operation: 'username-to-id-conversion',
              organizationId,
              username,
              error: 'No ID returned'
            });
          }
        } catch (error) {
          logger.error('Error during username to ID conversion', {
            operation: 'username-to-id-conversion',
            organizationId,
            username
          }, error as Error);
        }
      }

      if (!contact) {
        return NextResponse.json({
          error: 'Contact not found and could not create from username',
          details: username ? `Could not find or create contact for username: ${username}` : 'Contact ID not found'
        }, { status: 404 });
      }

      conversationMessages = contact.InstagramMessage.map(msg => ({
        content: msg.content,
        isFromUser: msg.isFromUser,
        timestamp: msg.timestamp.toISOString()
      }));
    }

    if (!conversationMessages || conversationMessages.length === 0) {
      return NextResponse.json({ error: 'No messages to analyze' }, { status: 400 });
    }

    // Calculate time since last message
    const lastMessage = conversationMessages[conversationMessages.length - 1];
    const lastMessageDate = new Date(lastMessage.timestamp);
    const now = new Date();
    const responseTimeHours = Math.floor((now.getTime() - lastMessageDate.getTime()) / (1000 * 60 * 60));

    // Format conversation for AI analysis
    const conversationText = conversationMessages
      .map(msg => `${msg.isFromUser ? 'User' : 'Bot'}: ${msg.content}`)
      .join('\n');

    // Create AI prompt for conversation analysis
    const analysisPrompt = `
Analyze this Instagram conversation and provide a detailed assessment:

CONVERSATION:
${conversationText}

LAST MESSAGE DATE: ${lastMessageDate.toISOString()}
HOURS SINCE LAST MESSAGE: ${responseTimeHours}

Please analyze this conversation and respond with a JSON object containing:

1. priority: Number from 1-5 where:
   - 5 = Highly engaged, ready to convert, immediate response needed
   - 4 = Engaged, showing strong interest, should respond within 4-6 hours
   - 3 = New follower or moderate engagement, standard 24h response
   - 2 = Low engagement, can wait 48-72 hours
   - 1 = Follow-up to non-responders, lowest priority

2. status: One of these stages based on conversation progress:
   - "new": Just started conversation, no meaningful engagement yet
   - "initial": Basic engagement, getting to know each other
   - "engaged": Active conversation, showing interest
   - "qualified": Expressed clear interest in services/products
   - "formsent": Ready for next step, form or meeting scheduled
   - "converted": Successfully converted to customer/client
   - "disqualified": Not a good fit, conversation should end

3. reason: Brief explanation for the priority and status assignment

4. engagementLevel: "low", "medium", or "high" based on:
   - Response frequency and speed
   - Message length and quality
   - Questions asked
   - Interest expressed

5. recommendations: Array of 2-3 specific action items for follow-up

Respond ONLY with valid JSON, no additional text.
`;

    let analysis: ConversationAnalysisResponse;
    let aiError = null;

    try {
      // Use the existing Claude implementation for conversation analysis
      const claudeResponse = await generateInstagramResponse({
        prompt: analysisPrompt,
        conversationHistory: conversationText,
        organizationId,
        disableCache: true
      });

      // Try to parse the Claude response as our analysis format
      let parsedAnalysis;
      try {
        // Claude might return the analysis in the message field
        const responseText = claudeResponse.message || claudeResponse.messages?.[0] || '';

        // Try to extract JSON from the response
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          parsedAnalysis = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in Claude response');
        }
      } catch (parseError) {
        // If parsing fails, create analysis from Claude's stage determination
        parsedAnalysis = {
          priority: claudeResponse.stage === 'qualified' ? 4 : 3,
          status: claudeResponse.stage || 'engaged',
          reason: `Claude analysis: ${claudeResponse.message?.substring(0, 100)}...`,
          engagementLevel: 'medium'
        };
      }

      analysis = {
        priority: parsedAnalysis.priority || 3,
        status: parsedAnalysis.status || 'engaged',
        reason: parsedAnalysis.reason || 'AI analysis completed',
        lastMessageDate: lastMessageDate.toISOString(),
        engagementLevel: parsedAnalysis.engagementLevel || 'medium',
        responseTime: responseTimeHours,
        recommendations: parsedAnalysis.recommendations || [
          'Continue conversation',
          'Ask qualifying questions',
          'Share relevant information'
        ]
      };

    } catch (error) {
      aiError = error instanceof Error ? error.message : 'Unknown AI error';

      logger.error('Failed to get AI analysis', {
        operation: 'analyze-conversation',
        organizationId,
        contactId: contactId || 'none',
        error: aiError
      }, error as Error);

      // Fallback analysis based on simple heuristics
      analysis = {
        priority: responseTimeHours < 4 ? 4 : 3,
        status: conversationMessages.length > 10 ? 'engaged' : 'initial',
        reason: 'AI analysis failed, using fallback heuristics',
        lastMessageDate: lastMessageDate.toISOString(),
        engagementLevel: conversationMessages.length > 5 ? 'medium' : 'low',
        responseTime: responseTimeHours,
        recommendations: ['Review conversation manually', 'Follow up within 24 hours']
      };
    }

    // Update contact in database if we have one
    if (contact && contactId) {
      const currentStage = contact.stage;
      const newStage = analysis.status as any;

      // Update contact stage and data
      await prisma.$transaction(async (tx) => {
        await tx.instagramContact.update({
          where: { id: contactId },
          data: {
            // priority: analysis.priority, // Temporarily disabled due to type issues
            stage: newStage,
            updatedAt: new Date()
          }
        });

        // Log the stage change if it occurred
        if (currentStage !== newStage) {
          await tx.stageChangeLog.create({
            data: {
              contactId,
              fromStage: currentStage,
              toStage: newStage,
              reason: `AI conversation analysis: ${analysis.reason}`,
              changedBy: 'ai_analysis',
              metadata: {
                action: 'ai_conversation_analysis',
                engagementLevel: analysis.engagementLevel,
                priority: analysis.priority,
                responseTimeHours: responseTimeHours,
                timestamp: new Date().toISOString()
              }
            }
          });
        }
      });

      // Handle disqualification cleanup if AI determined contact should be disqualified
      if (shouldTriggerDisqualificationCleanup(currentStage, newStage)) {
        logger.instagramConversation('AI marked contact as disqualified, triggering cleanup', {
          operation: 'ai-disqualification-cleanup',
          organizationId,
          contactId,
          reason: analysis.reason
        });

        const cleanupResult = await handleContactDisqualification(
          contactId,
          organizationId,
          `AI conversation analysis disqualification: ${analysis.reason}`
        );

        if (cleanupResult.success) {
          logger.instagramConversation('AI disqualification cleanup completed', {
            operation: 'ai-disqualification-cleanup',
            organizationId,
            contactId,
            actions: cleanupResult.actions
          });
        } else {
          logger.error('AI disqualification cleanup failed', {
            operation: 'ai-disqualification-cleanup',
            organizationId,
            contactId,
            error: cleanupResult.error
          });
        }
      }

      logger.instagramConversation('Updated contact with AI analysis', {
        operation: 'analyze-conversation',
        organizationId,
        contactId,
        priority: analysis.priority,
        status: analysis.status,
        engagementLevel: analysis.engagementLevel,
        stageChanged: currentStage !== newStage
      });
    }

    timer.end({
      organizationId,
      contactId: contactId || 'none',
      messageCount: conversationMessages.length,
      priority: analysis.priority,
      status: analysis.status,
      success: true
    });

    return NextResponse.json({
      success: true,
      analysis,
      contact: contact ? {
        id: contact.id,
        instagramNickname: contact.instagramNickname,
        stage: contact.stage
        // priority: contact.priority // Temporarily disabled due to type issues
      } : null
    });

  } catch (error) {
    logger.error('Error analyzing conversation', {
      operation: 'analyze-conversation'
    }, error as Error);

    timer.end({
      success: false,
      error: true
    });

    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
