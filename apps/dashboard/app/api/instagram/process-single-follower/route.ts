import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { generateInstagramResponse } from '@workspace/instagram-bot';
import { getAllConversations, getConversationMessages } from '~/lib/instagram-client';

const ProcessSingleFollowerSchema = z.object({
  followerId: z.string().uuid().optional(),
  instagramNickname: z.string().min(1).optional()
}).refine(data => data.followerId || data.instagramNickname, {
  message: "Either followerId or instagramNickname must be provided"
});

/**
 * Process a single Instagram follower immediately
 * This is for real-time processing: New Follower = Immediate Trigger
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = ProcessSingleFollowerSchema.parse(body);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get Instagram settings for API access
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings?.instagramToken) {
      return NextResponse.json(
        { success: false, error: 'No Instagram token found. Please configure Instagram settings first.' },
        { status: 400 }
      );
    }

    // Find the follower to process
    const follower = await prisma.instagramFollower.findFirst({
      where: {
        organizationId,
        ...(validatedData.followerId 
          ? { id: validatedData.followerId }
          : { instagramNickname: validatedData.instagramNickname }
        ),
        status: 'pending',
        automationEnabled: true
      }
    });

    if (!follower) {
      return NextResponse.json(
        { success: false, error: 'Follower not found or already processed' },
        { status: 404 }
      );
    }

    console.log(`🚀 Processing single follower: ${follower.instagramNickname}`);

    // Check if InstagramContact already exists
    const existingContact = await prisma.instagramContact.findFirst({
      where: {
        organizationId,
        instagramNickname: follower.instagramNickname
      }
    });

    if (existingContact) {
      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: { 
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Follower already exists as contact',
        data: {
          follower: follower.instagramNickname,
          existingContactId: existingContact.id,
          action: 'marked_as_contacted'
        }
      });
    }

    // Get all conversations from Instagram API
    console.log('Getting conversations from Instagram API...');
    const instagramApiResponse = await getAllConversations(instagramSettings.instagramToken);
    const instagramApiConversations = instagramApiResponse.data || [];

    // Check if this follower has a conversation
    const conversationData = instagramApiConversations.find((conversation: any) => {
      const participants = conversation.participants || [];
      if (participants.length >= 2) {
        // Skip the first participant (business account) and check the second one
        const participant = participants[1];
        return participant.username === follower.instagramNickname;
      }
      return false;
    });

    const hasConversation = !!conversationData;

    if (!hasConversation) {
      // No conversation - create contact with priority 3 and batch messages
      console.log(`📝 No conversation found for ${follower.instagramNickname} - creating with batch messages`);

      // Get available message batches
      const messageBatches = await prisma.messageBatch.findMany({
        where: {
          organizationId,
          isActive: true
        },
        include: {
          MessageBatchItem: {
            orderBy: { sequenceNumber: 'asc' }
          }
        }
      });

      const batchesWithMessages = messageBatches.filter(
        batch => batch.MessageBatchItem.length > 0
      );

      const randomBatch = batchesWithMessages.length > 0 
        ? batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)]
        : null;

      const newContact = await prisma.instagramContact.create({
        data: {
          organizationId,
          userId: session.user.id,
          instagramId: follower.instagramId,
          instagramNickname: follower.instagramNickname,
          avatar: follower.avatar,
          followerCount: follower.followerCount,
          isVerifiedUser: follower.isVerified,
          stage: 'new',
          priority: 3, // New followers get priority 3
          status: 'pending',
          messageCount: 0,
          isIgnored: false,
          isTakeControl: false,
          isConversionLinkSent: false,
          nextMessageAt: new Date(), // Ready to message immediately
          attackListStatus: 'pending',
          conversationSource: 'extension',
          batchId: randomBatch?.id
        }
      });

      // Create follow-ups from batch messages if available
      if (randomBatch && randomBatch.MessageBatchItem.length > 1) {
        for (const batchItem of randomBatch.MessageBatchItem) {
          if (batchItem.sequenceNumber > 1) { // Skip first message (it's the initial message)
            await prisma.instagramFollowUp.create({
              data: {
                contactId: newContact.id,
                message: batchItem.messageText,
                scheduledTime: new Date(Date.now() + ((batchItem.sequenceNumber - 1) * 24 * 60 * 60 * 1000)),
                status: 'external', // External so it appears in attack list
                sequenceNumber: batchItem.sequenceNumber - 1
              }
            });
          }
        }
      }

      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: { 
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        message: 'New follower processed with batch messages',
        data: {
          follower: follower.instagramNickname,
          contactId: newContact.id,
          priority: 3,
          batchId: randomBatch?.id,
          action: 'created_with_batch_messages'
        }
      });

    } else {
      // Has conversation - get full conversation and analyze with AI
      console.log(`💬 Conversation found for ${follower.instagramNickname} - analyzing with AI`);

      const conversationResponse = await getConversationMessages(conversationData.id, instagramSettings.instagramToken);

      // Format conversation history for AI
      let conversationHistory = '';
      if (conversationResponse && conversationResponse.data && conversationResponse.data.length > 0) {
        const conversation = conversationResponse.data[0];
        const messages = conversation.messages?.data || [];

        if (messages.length > 0) {
          const sortedMessages = messages.sort((a: any, b: any) =>
            new Date(a.created_time).getTime() - new Date(b.created_time).getTime()
          );

          conversationHistory = sortedMessages.map((msg: any) => {
            const sender = msg.from?.username || msg.from?.id || 'Unknown';
            const messageText = msg.message || '[Media/Attachment]';
            return `${sender}: ${messageText}`;
          }).join('\n');

          // Add last user interaction timestamp
          const lastUserMessage = sortedMessages
            .filter((msg: any) => msg.from?.username === follower.instagramNickname)
            .pop();

          if (lastUserMessage) {
            const lastInteractionTime = new Date(lastUserMessage.created_time);
            conversationHistory += `\n\nLAST USER INTERACTION: ${lastInteractionTime.toISOString()}`;
          }
        } else {
          conversationHistory = 'No messages found in conversation';
        }
      } else {
        conversationHistory = 'No messages found in conversation';
      }

      // Pass to AI for analysis with CONVERSATION GATHERING mode
      console.log(`🤖 Analyzing conversation with AI for ${follower.instagramNickname}...`);
      const aiResponse = await generateInstagramResponse({
        prompt: "CONVERSATION GATHERING",
        conversationHistory: conversationHistory,
        organizationId: organizationId
      });

      const aiPriority = aiResponse.priority || 3;
      const aiStage = aiResponse.stage || 'initial';

      const newContact = await prisma.instagramContact.create({
        data: {
          organizationId,
          userId: session.user.id,
          instagramId: follower.instagramId,
          instagramNickname: follower.instagramNickname,
          avatar: follower.avatar,
          followerCount: follower.followerCount,
          isVerifiedUser: follower.isVerified,
          stage: aiStage as any,
          priority: aiPriority,
          status: 'pending',
          messageCount: conversationResponse?.data?.[0]?.messages?.data?.length || 0,
          isIgnored: false,
          isTakeControl: false,
          isConversionLinkSent: false,
          nextMessageAt: new Date(), // Ready for follow-up
          attackListStatus: 'pending',
          conversationSource: 'extension'
        }
      });

      // Save conversation messages to database
      const messages = conversationResponse?.data?.[0]?.messages?.data || [];
      if (messages.length > 0) {
        for (const msg of messages) {
          try {
            await prisma.instagramMessage.create({
              data: {
                contactId: newContact.id,
                messageId: msg.id,
                content: msg.message || '[Media/Attachment]',
                isFromUser: msg.from?.username === follower.instagramNickname,
                timestamp: new Date(msg.created_time),
                mediaType: msg.attachments?.[0]?.mime_type || null,
                mediaUrl: msg.attachments?.[0]?.file_url || null
              }
            });
          } catch (error) {
            console.error(`Error saving message ${msg.id}:`, error);
          }
        }
      }

      // Add AI-generated follow-ups if provided
      if (aiResponse.followUps && aiResponse.followUps.length > 0) {
        for (let i = 0; i < aiResponse.followUps.length; i++) {
          const followUp = aiResponse.followUps[i];
          await prisma.instagramFollowUp.create({
            data: {
              contactId: newContact.id,
              message: followUp.message,
              scheduledTime: new Date(followUp.delayHours ? Date.now() + (followUp.delayHours * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000)),
              status: 'external', // External so it appears in attack list
              sequenceNumber: i + 1
            }
          });
        }
      }

      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: { 
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Follower processed with AI conversation analysis',
        data: {
          follower: follower.instagramNickname,
          contactId: newContact.id,
          priority: aiPriority,
          stage: aiStage,
          followUpsCreated: aiResponse.followUps?.length || 0,
          action: 'created_with_ai_analysis'
        }
      });
    }

  } catch (error) {
    console.error('Error processing single follower:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get single follower processing status
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const instagramNickname = searchParams.get('nickname');

    if (!instagramNickname) {
      return NextResponse.json(
        { success: false, error: 'nickname parameter required' },
        { status: 400 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Check follower status
    const follower = await prisma.instagramFollower.findFirst({
      where: {
        organizationId,
        instagramNickname
      }
    });

    // Check contact status
    const contact = await prisma.instagramContact.findFirst({
      where: {
        organizationId,
        instagramNickname
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        nickname: instagramNickname,
        follower: follower ? {
          id: follower.id,
          status: follower.status,
          isContacted: follower.isContacted,
          createdAt: follower.createdAt
        } : null,
        contact: contact ? {
          id: contact.id,
          priority: contact.priority,
          stage: contact.stage,
          attackListStatus: contact.attackListStatus,
          createdAt: contact.createdAt
        } : null,
        isProcessed: !!contact,
        needsProcessing: follower?.status === 'pending' && !contact
      }
    });

  } catch (error) {
    console.error('Error getting single follower status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
