import { redirect } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import { Page, PageBody, PageHeader, PagePrimaryBar } from '@workspace/ui/components/page';
import { TransitionProvider } from '~/hooks/use-transition-context';

import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { AdminSettingsForm } from '~/components/admin/admin-settings-form';
import { TestInstagramFollowers } from '~/components/admin/test-instagram-followers';
import { getAdminSettings } from '~/data/admin/get-admin-settings';

export default async function AdminSettingsPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  // Only allow SaaS admin (<EMAIL>) to access this page
  if (ctx.session.user.email !== '<EMAIL>') {
    redirect(`/organizations/${ctx.organization.slug}/instagram/prompts`);
  }

  const adminSettings = await getAdminSettings();

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Admin Settings"
              info="Configure system-wide settings, caching options, and test functionality"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <AdminSettingsForm adminSettings={adminSettings} />
            <TestInstagramFollowers />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
