import * as React from 'react';
import type { Metadata } from 'next';
import { unstable_noStore as noStore } from 'next/cache';

import { createTitle } from '~/lib/formatters';
import { getInstagramFollowUps } from '~/data/instagram/get-instagram-follow-ups';
import { FollowUpsDashboard } from '~/components/organizations/slug/instagram/follow-ups/follow-ups-dashboard';

export const metadata: Metadata = {
  title: createTitle('System Follow Ups (24h)')
};

export default async function InstagramFollowUpsPage(): Promise<React.JSX.Element> {
  // Disable caching for this page
  noStore();

  const followUpsData = await getInstagramFollowUps();

  return (
    <div className="container mx-auto py-6">
      <FollowUpsDashboard initialData={followUpsData} />
    </div>
  );
}
