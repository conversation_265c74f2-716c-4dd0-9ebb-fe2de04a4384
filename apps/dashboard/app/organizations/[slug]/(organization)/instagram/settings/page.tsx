import * as React from 'react';
import { type Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeftIcon } from 'lucide-react';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { Button } from '@workspace/ui/components/button';
import {
  Page,
  PageActions,
  PageBody,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';

// Using real components for production
import { InstagramSettingsForm } from '~/components/organizations/slug/instagram/settings/instagram-settings-form';
import { UsernameToIdTester } from '~/components/organizations/slug/instagram/settings/username-to-id-tester';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';
import { getInstagramSettings } from '~/data/instagram/get-instagram-settings';

export const metadata: Metadata = {
  title: createTitle('Settings')
};

export default async function InstagramSettingsPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();
  const settings = await getInstagramSettings();

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Settings"
              info="Configure your Instagram bot settings"
            />
            <PageActions>
              <Button
                variant="outline"
                size="sm"
                asChild
              >
                <Link href={replaceOrgSlug(routes.dashboard.organizations.slug.settings.organization.Index, ctx.organization.slug)}>
                  <ArrowLeftIcon className="mr-2 h-4 w-4" />
                  Back to Settings
                </Link>
              </Button>
            </PageActions>
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <UsernameToIdTester />
            <InstagramSettingsForm settings={settings} />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
