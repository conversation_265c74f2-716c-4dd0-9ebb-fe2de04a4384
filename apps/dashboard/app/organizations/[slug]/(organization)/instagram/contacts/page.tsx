import * as React from 'react';
import { type Metadata } from 'next';
import { redirect } from 'next/navigation';
import { unstable_noStore as noStore } from 'next/cache';

import {
  Page,
  PageActions,
  PageBody,
  PageHeader,
  PagePrimaryBar,
  PageSecondaryBar
} from '@workspace/ui/components/page';
import { EmptyState } from '@workspace/ui/components/empty-state';

import { getInstagramContacts } from '~/data/instagram/get-instagram-contacts';
import { getConversationsNotGathered } from '~/data/instagram/get-conversations-not-gathered';
import { InstagramContactsTable } from '~/components/organizations/slug/instagram/contacts/instagram-contacts-table';
import { ConversationsNotGatheredTable } from '~/components/organizations/slug/instagram/contacts/conversations-not-gathered-table';
import { ContactsTabs } from '~/components/organizations/slug/contacts/contacts-tabs';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Contacts')
};

interface InstagramContactsPageProps {
  searchParams?: {
    page?: string;
  };
}

export default async function InstagramContactsPage({ searchParams }: InstagramContactsPageProps): Promise<React.JSX.Element> {
  // Disable caching for this page to ensure new conversations appear immediately
  noStore();

  const currentPage = searchParams?.page ? parseInt(searchParams.page, 10) : 1;
  const contacts = await getInstagramContacts();
  const conversationsData = await getConversationsNotGathered({ page: currentPage });
  const hasContacts = contacts.length > 0;

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Contacts"
              info={`Total ${contacts.length} ${contacts.length === 1 ? 'contact' : 'contacts'}`}
            />
          </PagePrimaryBar>
          <PageSecondaryBar>
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Filter by stage:</span>
                <select className="rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                  <option value="all">All</option>
                  <option value="initial">Initial</option>
                  <option value="qualification">Qualification</option>
                  <option value="qualified">Qualified</option>
                  <option value="calendar_sent">Calendar Sent</option>
                  <option value="lead_magnet_sent">Lead Magnet Sent</option>
                  <option value="disqualified">Disqualified</option>
                </select>
              </div>
            </div>
          </PageSecondaryBar>
        </PageHeader>
        <PageBody className="p-6" disableScroll={hasContacts}>
          <div className="space-y-6">
            {!hasContacts ? (
              <EmptyState
                title="No contacts yet"
                description="Contacts will appear here once they message your Instagram account."
                icon="users"
              />
            ) : (
              <React.Suspense>
                <InstagramContactsTable contacts={contacts} />
              </React.Suspense>
            )}

            {/* Always show the conversations not gathered table */}
            <React.Suspense>
              <ConversationsNotGatheredTable 
                conversations={conversationsData.conversations} 
                total={conversationsData.total}
                totalPages={conversationsData.totalPages}
                currentPage={conversationsData.page}
                pageSize={conversationsData.pageSize}
              />
            </React.Suspense>
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
