'use client';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { AlertTriangle } from 'lucide-react';

export default function InstagramFollowersError(): React.JSX.Element {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Instagram Followers</CardTitle>
        <AlertTriangle className="h-4 w-4 text-destructive" />
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground">
          Failed to load follower statistics
        </div>
      </CardContent>
    </Card>
  );
}
